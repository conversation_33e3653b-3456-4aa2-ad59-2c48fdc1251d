Using pre-set license
Built from '6000.0/respin/6000.0.51f1-a206c6c19c75' branch; Version is '6000.0.51f1 (01c3ff5872c5) revision 115711'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'fr' Physical Memory: 7877 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-16T09:14:53Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.51f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
C:/Users/<USER>/Desktop/Eat&Meet
-logFile
Logs/AssetImportWorker2.log
-srvPort
49829
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/Desktop/Eat&Meet
C:/Users/<USER>/Desktop/Eat&Meet
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [4960]  Target information:

Player connection [4960]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 4050943174 [EditorId] 4050943174 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-P1RI1CO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [4960] Host joined multi-casting on [***********:54997]...
Player connection [4960] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.91 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.51f1 (01c3ff5872c5)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Desktop/Eat&Meet/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: Intel(R) UHD Graphics 770 (ID=0x4690)
    Vendor:   Intel
    VRAM:     3938 MB
    Driver:   32.0.101.6078
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56600
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.003548 seconds.
- Loaded All Assemblies, in  0.565 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 219 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.540 seconds
Domain Reload Profiling: 1102ms
	BeginReloadAssembly (160ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (55ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (74ms)
	LoadAllAssembliesAndSetupDomain (259ms)
		LoadAssemblies (159ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (253ms)
			TypeCache.Refresh (250ms)
				TypeCache.ScanAssembly (226ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (540ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (491ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (312ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (43ms)
			ProcessInitializeOnLoadAttributes (89ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.627 seconds
Refreshing native plugins compatible for Editor in 0.67 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.626 seconds
Domain Reload Profiling: 1250ms
	BeginReloadAssembly (144ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (20ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (400ms)
		LoadAssemblies (291ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (196ms)
			TypeCache.Refresh (147ms)
				TypeCache.ScanAssembly (130ms)
			BuildScriptInfoCaches (36ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (626ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (464ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (300ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 0.70 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 198 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5982 unused Assets / (7.1 MB). Loaded Objects now: 6632.
Memory consumption went from 157.0 MB to 149.9 MB.
Total: 9.137500 ms (FindLiveObjects: 1.012900 ms CreateObjectMapping: 0.386700 ms MarkObjects: 4.663400 ms  DeleteObjects: 3.072900 ms)

========================================================================
Received Import Request.
  Time since last request: 4654.519011 seconds.
  path: Assets/Prefabs/Dog_BorderCollie_01.prefab
  artifactKey: Guid(d3273ee892cdcf749a22b49daf393a91) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Dog_BorderCollie_01.prefab using Guid(d3273ee892cdcf749a22b49daf393a91) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'df818837e23bce7a5ae479022005d1c1') in 0.534899 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 273

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.763 seconds
Refreshing native plugins compatible for Editor in 0.74 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.699 seconds
Domain Reload Profiling: 1462ms
	BeginReloadAssembly (266ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (34ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (69ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (419ms)
		LoadAssemblies (338ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (184ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (163ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (699ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (523ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (109ms)
			ProcessInitializeOnLoadAttributes (345ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 1.42 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5978 unused Assets / (6.7 MB). Loaded Objects now: 6682.
Memory consumption went from 160.0 MB to 153.3 MB.
Total: 14.589100 ms (FindLiveObjects: 1.713700 ms CreateObjectMapping: 0.913100 ms MarkObjects: 6.391700 ms  DeleteObjects: 5.568400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.726 seconds
Refreshing native plugins compatible for Editor in 1.29 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.844 seconds
Domain Reload Profiling: 1570ms
	BeginReloadAssembly (221ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (58ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (416ms)
		LoadAssemblies (326ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (177ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (144ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (844ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (649ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (124ms)
			ProcessInitializeOnLoadAttributes (437ms)
			ProcessInitializeOnLoadMethodAttributes (66ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 0.94 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (7.3 MB). Loaded Objects now: 6684.
Memory consumption went from 157.9 MB to 150.6 MB.
Total: 13.275800 ms (FindLiveObjects: 1.137000 ms CreateObjectMapping: 0.844100 ms MarkObjects: 5.423000 ms  DeleteObjects: 5.869200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.702 seconds
Refreshing native plugins compatible for Editor in 0.80 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.719 seconds
Domain Reload Profiling: 1422ms
	BeginReloadAssembly (206ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (412ms)
		LoadAssemblies (332ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (173ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (146ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (719ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (550ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (120ms)
			ProcessInitializeOnLoadAttributes (332ms)
			ProcessInitializeOnLoadMethodAttributes (75ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 2.05 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (7.3 MB). Loaded Objects now: 6686.
Memory consumption went from 158.0 MB to 150.6 MB.
Total: 15.337700 ms (FindLiveObjects: 0.966700 ms CreateObjectMapping: 0.866900 ms MarkObjects: 6.368000 ms  DeleteObjects: 7.133100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.656 seconds
Refreshing native plugins compatible for Editor in 0.83 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.634 seconds
Domain Reload Profiling: 1291ms
	BeginReloadAssembly (198ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (392ms)
		LoadAssemblies (319ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (166ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (144ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (634ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (476ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (104ms)
			ProcessInitializeOnLoadAttributes (311ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 1.49 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (6.8 MB). Loaded Objects now: 6688.
Memory consumption went from 157.9 MB to 151.1 MB.
Total: 15.210900 ms (FindLiveObjects: 0.882200 ms CreateObjectMapping: 0.890400 ms MarkObjects: 7.001000 ms  DeleteObjects: 6.434400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.672 seconds
Refreshing native plugins compatible for Editor in 0.84 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.732 seconds
Domain Reload Profiling: 1405ms
	BeginReloadAssembly (205ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (401ms)
		LoadAssemblies (320ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (175ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (154ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (733ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (548ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (341ms)
			ProcessInitializeOnLoadMethodAttributes (72ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (7.3 MB). Loaded Objects now: 6690.
Memory consumption went from 157.9 MB to 150.6 MB.
Total: 16.097300 ms (FindLiveObjects: 1.513700 ms CreateObjectMapping: 1.632200 ms MarkObjects: 6.828600 ms  DeleteObjects: 6.119200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.674 seconds
Refreshing native plugins compatible for Editor in 1.00 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.622 seconds
Domain Reload Profiling: 1296ms
	BeginReloadAssembly (201ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (398ms)
		LoadAssemblies (319ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (163ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (142ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (623ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (479ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (111ms)
			ProcessInitializeOnLoadAttributes (304ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 0.69 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (6.6 MB). Loaded Objects now: 6692.
Memory consumption went from 158.0 MB to 151.4 MB.
Total: 13.853200 ms (FindLiveObjects: 0.736500 ms CreateObjectMapping: 0.771000 ms MarkObjects: 6.651600 ms  DeleteObjects: 5.692400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.672 seconds
Refreshing native plugins compatible for Editor in 0.79 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.652 seconds
Domain Reload Profiling: 1324ms
	BeginReloadAssembly (203ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (53ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (381ms)
		LoadAssemblies (330ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (144ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (123ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (652ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (474ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (107ms)
			ProcessInitializeOnLoadAttributes (308ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 1.71 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (7.1 MB). Loaded Objects now: 6694.
Memory consumption went from 158.0 MB to 150.9 MB.
Total: 15.187400 ms (FindLiveObjects: 1.225900 ms CreateObjectMapping: 1.736200 ms MarkObjects: 5.446400 ms  DeleteObjects: 6.776800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 722.748576 seconds.
  path: Assets/Course Library/Animals/Farm/Animal_Cow_Brown.prefab
  artifactKey: Guid(50f1d192ccb21a24b95a7e4e403a9bb5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Course Library/Animals/Farm/Animal_Cow_Brown.prefab using Guid(50f1d192ccb21a24b95a7e4e403a9bb5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9707ca2aa480285d2edb261ace072cdb') in 0.5599982 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 126

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Course Library/Animals/Forest/Animal_Fox_01.prefab
  artifactKey: Guid(e3245acddde40bf49aa555d6bdc14998) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Course Library/Animals/Forest/Animal_Fox_01.prefab using Guid(e3245acddde40bf49aa555d6bdc14998) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd977c3f7f4b003592165464c77efd99d') in 0.04804 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 67

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Course Library/Animals/Farm/Animal_Horse_Brown.prefab
  artifactKey: Guid(68a45bf17ac566545b70a13c90a7ee65) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Course Library/Animals/Farm/Animal_Horse_Brown.prefab using Guid(68a45bf17ac566545b70a13c90a7ee65) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fa7811e6b16a4e1966d550a60b64a8da') in 0.0524736 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 125

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Course Library/Animals/Forest/Animal_Doe_02.prefab
  artifactKey: Guid(2fdf2b8525a6fa54ba867925b16564e3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Course Library/Animals/Forest/Animal_Doe_02.prefab using Guid(2fdf2b8525a6fa54ba867925b16564e3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '307ab57453b2231e5e017818aa74ee80') in 0.0462238 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 59

========================================================================
Received Import Request.
  Time since last request: 0.561121 seconds.
  path: Assets/Course Library/Animals/Forest/Animal_Moose_01.prefab
  artifactKey: Guid(72d1e1ec46e639c47a81658b0d9e230e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Course Library/Animals/Forest/Animal_Moose_01.prefab using Guid(72d1e1ec46e639c47a81658b0d9e230e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6124db8e328f7d36a9db862b253e5ee7') in 0.0294166 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 59

========================================================================
Received Import Request.
  Time since last request: 0.000068 seconds.
  path: Assets/Course Library/Animals/Farm/Animal_Rooster_Brown.prefab
  artifactKey: Guid(3fe800c166ae0d04881a20247bde736d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Course Library/Animals/Farm/Animal_Rooster_Brown.prefab using Guid(3fe800c166ae0d04881a20247bde736d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f471d719ff96e9566d8c563d81c7de7b') in 0.0572536 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 85

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Course Library/Animals/Forest/Animal_Stag_02.prefab
  artifactKey: Guid(e2b54f18a442ae84cb37b05e649edfc0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Course Library/Animals/Forest/Animal_Stag_02.prefab using Guid(e2b54f18a442ae84cb37b05e649edfc0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9047eda1c33576cba6d4fd2544fd6728') in 0.03334 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 59

========================================================================
Received Import Request.
  Time since last request: 1.231093 seconds.
  path: Assets/Course Library/_Source_Files/FBX/Animations.fbx
  artifactKey: Guid(d55a3dafe23105e409ee70d6605cbd49) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Course Library/_Source_Files/FBX/Animations.fbx using Guid(d55a3dafe23105e409ee70d6605cbd49) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '95e3f258fc0e4bb37942a5ecd65ba75c') in 0.0202276 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 79

========================================================================
Received Import Request.
  Time since last request: 0.518441 seconds.
  path: Assets/Course Library/_Source_Files/FBX/Animations_Static.fbx
  artifactKey: Guid(7039333667ba45840b1411867278ee2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Course Library/_Source_Files/FBX/Animations_Static.fbx using Guid(7039333667ba45840b1411867278ee2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9b97db34e3d4677079c285d92df500f9') in 0.0071833 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 48

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.767 seconds
Refreshing native plugins compatible for Editor in 1.18 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.691 seconds
Domain Reload Profiling: 1459ms
	BeginReloadAssembly (252ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (26ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (77ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (440ms)
		LoadAssemblies (363ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (169ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (149ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (692ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (532ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (122ms)
			ProcessInitializeOnLoadAttributes (324ms)
			ProcessInitializeOnLoadMethodAttributes (63ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 1.48 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5978 unused Assets / (7.2 MB). Loaded Objects now: 6716.
Memory consumption went from 159.3 MB to 152.1 MB.
Total: 16.291200 ms (FindLiveObjects: 0.959400 ms CreateObjectMapping: 0.879500 ms MarkObjects: 7.206900 ms  DeleteObjects: 7.241200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.667 seconds
Refreshing native plugins compatible for Editor in 0.67 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.683 seconds
Domain Reload Profiling: 1352ms
	BeginReloadAssembly (207ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (53ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (397ms)
		LoadAssemblies (310ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (172ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (151ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (684ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (522ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (116ms)
			ProcessInitializeOnLoadAttributes (321ms)
			ProcessInitializeOnLoadMethodAttributes (65ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 1.05 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (7.0 MB). Loaded Objects now: 6718.
Memory consumption went from 159.2 MB to 152.2 MB.
Total: 16.240500 ms (FindLiveObjects: 1.463500 ms CreateObjectMapping: 0.945500 ms MarkObjects: 6.301000 ms  DeleteObjects: 7.527600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.631 seconds
Refreshing native plugins compatible for Editor in 1.08 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.708 seconds
Domain Reload Profiling: 1341ms
	BeginReloadAssembly (198ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (368ms)
		LoadAssemblies (300ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (159ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (137ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (709ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (537ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (129ms)
			ProcessInitializeOnLoadAttributes (318ms)
			ProcessInitializeOnLoadMethodAttributes (68ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 1.16 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (6.9 MB). Loaded Objects now: 6720.
Memory consumption went from 159.3 MB to 152.3 MB.
Total: 16.062600 ms (FindLiveObjects: 1.804700 ms CreateObjectMapping: 1.602900 ms MarkObjects: 6.215500 ms  DeleteObjects: 6.437800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.678 seconds
Refreshing native plugins compatible for Editor in 0.67 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.779 seconds
Domain Reload Profiling: 1458ms
	BeginReloadAssembly (202ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (402ms)
		LoadAssemblies (331ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (171ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (150ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (779ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (601ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (402ms)
			ProcessInitializeOnLoadMethodAttributes (76ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 1.32 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (7.3 MB). Loaded Objects now: 6722.
Memory consumption went from 159.3 MB to 152.0 MB.
Total: 16.242000 ms (FindLiveObjects: 1.867000 ms CreateObjectMapping: 1.582000 ms MarkObjects: 6.610300 ms  DeleteObjects: 6.180100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.648 seconds
Refreshing native plugins compatible for Editor in 0.65 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.614 seconds
Domain Reload Profiling: 1265ms
	BeginReloadAssembly (202ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (378ms)
		LoadAssemblies (316ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (153ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (130ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (614ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (475ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (104ms)
			ProcessInitializeOnLoadAttributes (308ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 1.18 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (7.0 MB). Loaded Objects now: 6724.
Memory consumption went from 159.3 MB to 152.3 MB.
Total: 15.437700 ms (FindLiveObjects: 0.956400 ms CreateObjectMapping: 1.329100 ms MarkObjects: 6.267100 ms  DeleteObjects: 6.883500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.636 seconds
Refreshing native plugins compatible for Editor in 0.95 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.630 seconds
Domain Reload Profiling: 1266ms
	BeginReloadAssembly (200ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (373ms)
		LoadAssemblies (318ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (157ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (138ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (631ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (472ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (103ms)
			ProcessInitializeOnLoadAttributes (308ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 1.18 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (6.8 MB). Loaded Objects now: 6726.
Memory consumption went from 159.4 MB to 152.6 MB.
Total: 12.637900 ms (FindLiveObjects: 0.870900 ms CreateObjectMapping: 0.750600 ms MarkObjects: 6.140000 ms  DeleteObjects: 4.874700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  3.156 seconds
Refreshing native plugins compatible for Editor in 1.79 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.873 seconds
Domain Reload Profiling: 4033ms
	BeginReloadAssembly (408ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (56ms)
	RebuildCommonClasses (140ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (2574ms)
		LoadAssemblies (2590ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (264ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (224ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (873ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (649ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (137ms)
			ProcessInitializeOnLoadAttributes (417ms)
			ProcessInitializeOnLoadMethodAttributes (67ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 1.66 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (7.5 MB). Loaded Objects now: 6728.
Memory consumption went from 159.4 MB to 151.9 MB.
Total: 18.957600 ms (FindLiveObjects: 2.226500 ms CreateObjectMapping: 1.299100 ms MarkObjects: 7.617400 ms  DeleteObjects: 7.810900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.677 seconds
Refreshing native plugins compatible for Editor in 1.03 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.655 seconds
Domain Reload Profiling: 1333ms
	BeginReloadAssembly (220ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (380ms)
		LoadAssemblies (340ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (147ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (126ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (656ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (493ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (314ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 1.05 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (7.1 MB). Loaded Objects now: 6730.
Memory consumption went from 159.4 MB to 152.3 MB.
Total: 16.099500 ms (FindLiveObjects: 0.735400 ms CreateObjectMapping: 0.656500 ms MarkObjects: 7.137000 ms  DeleteObjects: 7.568400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.700 seconds
Refreshing native plugins compatible for Editor in 0.58 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.608 seconds
Domain Reload Profiling: 1309ms
	BeginReloadAssembly (229ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (64ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (396ms)
		LoadAssemblies (333ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (165ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (143ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (609ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (466ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (105ms)
			ProcessInitializeOnLoadAttributes (293ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 1.33 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (7.0 MB). Loaded Objects now: 6732.
Memory consumption went from 159.4 MB to 152.5 MB.
Total: 12.702900 ms (FindLiveObjects: 0.716000 ms CreateObjectMapping: 0.591300 ms MarkObjects: 6.075000 ms  DeleteObjects: 5.317300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.626 seconds
Refreshing native plugins compatible for Editor in 0.67 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.672 seconds
Domain Reload Profiling: 1300ms
	BeginReloadAssembly (198ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (39ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (356ms)
		LoadAssemblies (306ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (147ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (120ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (672ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (517ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (113ms)
			ProcessInitializeOnLoadAttributes (332ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 1.46 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (7.0 MB). Loaded Objects now: 6734.
Memory consumption went from 159.5 MB to 152.5 MB.
Total: 16.252900 ms (FindLiveObjects: 0.999700 ms CreateObjectMapping: 1.877400 ms MarkObjects: 5.836700 ms  DeleteObjects: 7.537300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.675 seconds
Refreshing native plugins compatible for Editor in 0.62 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.664 seconds
Domain Reload Profiling: 1340ms
	BeginReloadAssembly (219ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (53ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (381ms)
		LoadAssemblies (326ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (153ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (132ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (665ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (518ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (111ms)
			ProcessInitializeOnLoadAttributes (332ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 1.36 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (6.7 MB). Loaded Objects now: 6736.
Memory consumption went from 159.5 MB to 152.8 MB.
Total: 16.430900 ms (FindLiveObjects: 1.260300 ms CreateObjectMapping: 1.222200 ms MarkObjects: 6.292900 ms  DeleteObjects: 7.653200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.821 seconds
Refreshing native plugins compatible for Editor in 1.07 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.793 seconds
Domain Reload Profiling: 1615ms
	BeginReloadAssembly (256ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (60ms)
	RebuildCommonClasses (49ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (470ms)
		LoadAssemblies (400ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (190ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (166ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (794ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (595ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (111ms)
			ProcessInitializeOnLoadAttributes (379ms)
			ProcessInitializeOnLoadMethodAttributes (81ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (6.5 MB). Loaded Objects now: 6738.
Memory consumption went from 159.6 MB to 153.0 MB.
Total: 16.706900 ms (FindLiveObjects: 1.214300 ms CreateObjectMapping: 1.233800 ms MarkObjects: 7.173600 ms  DeleteObjects: 7.082200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.847 seconds
Refreshing native plugins compatible for Editor in 0.72 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.667 seconds
Domain Reload Profiling: 2514ms
	BeginReloadAssembly (201ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (1579ms)
		LoadAssemblies (1488ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (179ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (148ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (668ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (509ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (117ms)
			ProcessInitializeOnLoadAttributes (330ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 0.96 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (6.6 MB). Loaded Objects now: 6740.
Memory consumption went from 159.6 MB to 153.0 MB.
Total: 11.674700 ms (FindLiveObjects: 0.969200 ms CreateObjectMapping: 0.475200 ms MarkObjects: 5.955200 ms  DeleteObjects: 4.273000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.651 seconds
Refreshing native plugins compatible for Editor in 0.70 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.701 seconds
Domain Reload Profiling: 1353ms
	BeginReloadAssembly (208ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (373ms)
		LoadAssemblies (320ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (153ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (135ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (702ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (536ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (113ms)
			ProcessInitializeOnLoadAttributes (327ms)
			ProcessInitializeOnLoadMethodAttributes (74ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 1.05 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (7.6 MB). Loaded Objects now: 6742.
Memory consumption went from 159.6 MB to 152.1 MB.
Total: 18.700100 ms (FindLiveObjects: 2.282300 ms CreateObjectMapping: 1.182400 ms MarkObjects: 6.030500 ms  DeleteObjects: 9.198500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.904 seconds
Refreshing native plugins compatible for Editor in 0.73 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.909 seconds
Domain Reload Profiling: 1816ms
	BeginReloadAssembly (285ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (72ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (528ms)
		LoadAssemblies (421ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (230ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (198ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (910ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (702ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (124ms)
			ProcessInitializeOnLoadAttributes (472ms)
			ProcessInitializeOnLoadMethodAttributes (78ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 1.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (7.3 MB). Loaded Objects now: 6744.
Memory consumption went from 159.7 MB to 152.4 MB.
Total: 17.012100 ms (FindLiveObjects: 2.199100 ms CreateObjectMapping: 1.106000 ms MarkObjects: 6.355200 ms  DeleteObjects: 7.349400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5970 unused Assets / (7.0 MB). Loaded Objects now: 6744.
Memory consumption went from 159.9 MB to 152.9 MB.
Total: 15.226100 ms (FindLiveObjects: 0.792100 ms CreateObjectMapping: 0.496300 ms MarkObjects: 7.020400 ms  DeleteObjects: 6.915500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.758 seconds
Refreshing native plugins compatible for Editor in 1.32 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.800 seconds
Domain Reload Profiling: 1557ms
	BeginReloadAssembly (261ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (61ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (413ms)
		LoadAssemblies (375ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (164ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (141ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (801ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (586ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (132ms)
			ProcessInitializeOnLoadAttributes (367ms)
			ProcessInitializeOnLoadMethodAttributes (64ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 1.20 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (7.8 MB). Loaded Objects now: 6746.
Memory consumption went from 159.7 MB to 151.9 MB.
Total: 10.484200 ms (FindLiveObjects: 0.698400 ms CreateObjectMapping: 0.704900 ms MarkObjects: 4.885400 ms  DeleteObjects: 4.193500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.769 seconds
Refreshing native plugins compatible for Editor in 1.71 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.830 seconds
Domain Reload Profiling: 1599ms
	BeginReloadAssembly (234ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (444ms)
		LoadAssemblies (374ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (183ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (161ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (831ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (670ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (123ms)
			ProcessInitializeOnLoadAttributes (446ms)
			ProcessInitializeOnLoadMethodAttributes (77ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 1.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (6.7 MB). Loaded Objects now: 6748.
Memory consumption went from 159.7 MB to 153.0 MB.
Total: 16.849000 ms (FindLiveObjects: 1.432600 ms CreateObjectMapping: 1.650300 ms MarkObjects: 6.611300 ms  DeleteObjects: 7.152800 ms)

Prepare: number of updated asset objects reloaded= 0
