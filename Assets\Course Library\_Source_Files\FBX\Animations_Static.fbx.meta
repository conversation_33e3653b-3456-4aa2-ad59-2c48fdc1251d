fileFormatVersion: 2
guid: 7039333667ba45840b1411867278ee2e
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: //RootNode
    100002: Body_jnt
    100004: Foot_Left_jnt
    100006: Foot_Left_jnt1
    100008: Foot_Right_jnt
    100010: Foot_Right_jnt1
    100012: Hand_Left_jnt
    100014: Hand_Right_jnt
    100016: Hat_jnt
    100018: Hat_jnt1
    100020: Head_jnt
    100022: Hips_jnt
    100024: LowerArm_Left_jnt
    100026: LowerArm_Right_jnt
    100028: LowerLeg_Left_jnt
    100030: LowerLeg_Right_jnt
    100032: Root_jnt
    100034: Spine_jnt
    100036: UpperArm_Left_jnt
    100038: UpperArm_Right_jnt
    100040: UpperLeg_Left_jnt
    100042: UpperLeg_Right_jnt
    400000: //RootNode
    400002: Body_jnt
    400004: Foot_Left_jnt
    400006: Foot_Left_jnt1
    400008: Foot_Right_jnt
    400010: Foot_Right_jnt1
    400012: Hand_Left_jnt
    400014: Hand_Right_jnt
    400016: Hat_jnt
    400018: Hat_jnt1
    400020: Head_jnt
    400022: Hips_jnt
    400024: LowerArm_Left_jnt
    400026: LowerArm_Right_jnt
    400028: LowerLeg_Left_jnt
    400030: LowerLeg_Right_jnt
    400032: Root_jnt
    400034: Spine_jnt
    400036: UpperArm_Left_jnt
    400038: UpperArm_Right_jnt
    400040: UpperLeg_Left_jnt
    400042: UpperLeg_Right_jnt
    7400000: Walk_Static
    7400002: Run_Static
    9500000: //RootNode
    2186277476908879412: ImportLogs
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 0
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: "Copied Avatar Rig Configuration mis-match. Bone length in
      copied configuration does not match position in animation file:\n\t'Body_jnt'
      : position error = 22.159853 mm\n"
    animationImportErrors: 
    animationImportWarnings: "\nClip 'Take 001' has import animation warnings that
      might lower retargeting quality:\nNote: Activate translation DOF on avatar to
      improve retargeting quality.\n\t'Body_jnt' has translation animation that will
      be discarded.\n"
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: Walk_Static
      takeName: Take 001
      firstFrame: 52
      lastFrame: 79
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Run_Static
      takeName: Take 001
      firstFrame: 81
      lastFrame: 98
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 0.01
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 0
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 2
    tangentImportMode: 2
    normalCalculationMode: 0
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 1
    blendShapeNormalImportMode: 2
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human:
    - boneName: Hips_jnt
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpperLeg_Left_jnt
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpperLeg_Right_jnt
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LowerLeg_Left_jnt
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LowerLeg_Right_jnt
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Foot_Left_jnt
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Foot_Right_jnt
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Body_jnt
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine_jnt
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head_jnt
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpperArm_Left_jnt
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpperArm_Right_jnt
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LowerArm_Left_jnt
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LowerArm_Right_jnt
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Hand_Left_jnt
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Hand_Right_jnt
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Hat_jnt
      humanName: LeftEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Hat_jnt1
      humanName: RightEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Animations(Clone)
      parentName: 
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Root_jnt
      parentName: 
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Hips_jnt
      parentName: 
      position: {x: -0, y: 1.0449876, z: 0}
      rotation: {x: -0.010444531, y: -0.03883079, z: 0.25953355, w: 0.9648966}
      scale: {x: 1, y: 1, z: 1}
    - name: Body_jnt
      parentName: 
      position: {x: 0.009555898, y: 0.016478358, z: 5.0122732e-18}
      rotation: {x: 0.013083731, y: -0.02271187, z: 0.866206, w: -0.49899927}
      scale: {x: 1, y: 1, z: 1}
    - name: Spine_jnt
      parentName: 
      position: {x: -0.6703252, y: 1.488421e-16, z: 0}
      rotation: {x: 0, y: -0, z: -1.6657682e-15, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Head_jnt
      parentName: 
      position: {x: -0.3106261, y: 6.897285e-17, z: 0}
      rotation: {x: 0, y: -0, z: -1.9984012e-15, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Hat_jnt
      parentName: 
      position: {x: -0.54227096, y: 1.2040834e-16, z: 0}
      rotation: {x: 3.593872e-16, y: -1.4046645e-16, z: 0.7071068, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: Hat_jnt1
      parentName: 
      position: {x: -0.54227096, y: 1.2040834e-16, z: 0}
      rotation: {x: 4.04522e-16, y: -9.778496e-17, z: 0.7071068, w: 0.7071068}
      scale: {x: -1, y: -1, z: -1}
    - name: UpperArm_Left_jnt
      parentName: 
      position: {x: -0.21910845, y: -0.565232, z: 0}
      rotation: {x: 0, y: -0, z: -0.7071068, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: LowerArm_Left_jnt
      parentName: 
      position: {x: 0.471188, y: -2.842171e-16, z: 5.77041e-17}
      rotation: {x: 0, y: -0, z: -3.330669e-15, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Hand_Left_jnt
      parentName: 
      position: {x: 0.4403, y: 0, z: 5.39209e-17}
      rotation: {x: 6.123234e-17, y: 1, z: -6.123234e-17, w: -2.2816785e-15}
      scale: {x: 1, y: 1, z: 1}
    - name: UpperArm_Right_jnt
      parentName: 
      position: {x: -0.21910845, y: 0.565232, z: 0}
      rotation: {x: 0.7071068, y: -0.7071068, z: 8.659561e-17, w: 0}
      scale: {x: 1, y: 1, z: 1}
    - name: LowerArm_Right_jnt
      parentName: 
      position: {x: -0.47118998, y: 5.684342e-16, z: -5.770413e-17}
      rotation: {x: -6.627657e-19, y: -3.586922e-21, z: 5.0220245e-16, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Hand_Right_jnt
      parentName: 
      position: {x: -0.4403, y: -8.5265126e-16, z: -5.3921198e-17}
      rotation: {x: 6.123234e-17, y: 1, z: -6.123234e-17, w: 1.8375893e-15}
      scale: {x: 1, y: 1, z: 1}
    - name: UpperLeg_Left_jnt
      parentName: 
      position: {x: -0.25698245, y: -1.4210854e-16, z: 0}
      rotation: {x: 0.028649574, y: 0.030012136, z: 0.49874875, w: 0.865753}
      scale: {x: 1, y: 1, z: 1}
    - name: LowerLeg_Left_jnt
      parentName: 
      position: {x: -0.37452164, y: -3.5527136e-17, z: -2.7105054e-22}
      rotation: {x: -0.7075902, y: 0.0000064468913, z: -0.0000061459123, w: 0.706623}
      scale: {x: 1, y: 1, z: 1}
    - name: Foot_Left_jnt
      parentName: 
      position: {x: -0.38874373, y: 1.2252568e-17, z: 7.3426066e-12}
      rotation: {x: 0.50674546, y: 0.49426788, z: -0.50519735, w: 0.49364367}
      scale: {x: 1, y: 1, z: 1}
    - name: UpperLeg_Right_jnt
      parentName: 
      position: {x: 0.12763838, y: -0.22304337, z: 0}
      rotation: {x: -0.49867836, y: 0.8654293, z: 0.046628922, w: -0.013333483}
      scale: {x: 1, y: 1, z: 1}
    - name: LowerLeg_Right_jnt
      parentName: 
      position: {x: 0.374522, y: 2.4868996e-16, z: -1.0987412e-12}
      rotation: {x: -0.70410997, y: -0.000030960506, z: 0.00005181963, w: 0.710091}
      scale: {x: 1, y: 1, z: 1}
    - name: Foot_Right_jnt
      parentName: 
      position: {x: 0.388744, y: 8.197135e-12, z: -3.6907224e-11}
      rotation: {x: 0.49583417, y: 0.5067274, z: -0.49273258, w: 0.50456953}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.542
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: Root_jnt
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 0
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: d55a3dafe23105e409ee70d6605cbd49,
    type: 3}
  animationType: 3
  humanoidOversampling: 1
  additionalBone: 1
  userData: 
  assetBundleName: 
  assetBundleVariant: 
