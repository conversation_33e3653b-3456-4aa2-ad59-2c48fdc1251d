fileFormatVersion: 2
guid: 693459689483f9e4aa5a1a6e8b37269e
timeCreated: **********
licenseType: Store
ModelImporter:
  serializedVersion: 18
  fileIDToRecycleName:
    100000: ChickenRig_Head_TopSHJnt
    100002: Chicken<PERSON>ig_l_Leg_AnkleSHJnt
    100004: ChickenRig_l_Leg_BallSHJnt
    100006: Chicken<PERSON>ig_l_Leg_HipSHJnt
    100008: ChickenRig_l_Leg_KneeSHJnt
    100010: ChickenRig_l_Leg_ToeSHJnt
    100012: ChickenRig_Neck_01SHJnt
    100014: ChickenRig_Neck_02SHJnt
    100016: ChickenRig_Neck_TopSHJnt
    100018: ChickenRig_r_Leg_AnkleSHJnt
    100020: ChickenRig_r_Leg_BallSHJnt
    100022: ChickenRig_r_Leg_HipSHJnt
    100024: ChickenRig_r_Leg_KneeSHJnt
    100026: ChickenRig_r_Leg_ToeSHJnt
    100028: ChickenRig_ROOTSHJnt
    100030: Chicken<PERSON>ig_SHJntGrp
    100032: ChickenRig_Tail_01SHJnt
    100034: ChickenRig_Tail_02SHJnt
    100036: ChickenRig_Tail_TopSHJnt
    100038: //RootNode
    100040: SA_Animals_Chicken_Brown_U
    100042: SA_Animals_Chicken_White
    100044: SA_Animals_Duck_Brown_U
    100046: SA_Animals_Duck_Green
    100048: SA_Animals_Duck_White_U
    100050: SA_Animals_Rooster_Black
    100052: SA_Animals_Rooster_Brown_U
    100054: SA_Animals_Chicken_Brown
    100056: SA_Animals_Duck_Brown
    100058: SA_Animals_Duck_White
    100060: SA_Animals_Rooster_Brown
    400000: ChickenRig_Head_TopSHJnt
    400002: ChickenRig_l_Leg_AnkleSHJnt
    400004: ChickenRig_l_Leg_BallSHJnt
    400006: ChickenRig_l_Leg_HipSHJnt
    400008: ChickenRig_l_Leg_KneeSHJnt
    400010: ChickenRig_l_Leg_ToeSHJnt
    400012: ChickenRig_Neck_01SHJnt
    400014: ChickenRig_Neck_02SHJnt
    400016: ChickenRig_Neck_TopSHJnt
    400018: ChickenRig_r_Leg_AnkleSHJnt
    400020: ChickenRig_r_Leg_BallSHJnt
    400022: ChickenRig_r_Leg_HipSHJnt
    400024: ChickenRig_r_Leg_KneeSHJnt
    400026: ChickenRig_r_Leg_ToeSHJnt
    400028: ChickenRig_ROOTSHJnt
    400030: ChickenRig_SHJntGrp
    400032: ChickenRig_Tail_01SHJnt
    400034: ChickenRig_Tail_02SHJnt
    400036: ChickenRig_Tail_TopSHJnt
    400038: //RootNode
    400040: SA_Animals_Chicken_Brown_U
    400042: SA_Animals_Chicken_White
    400044: SA_Animals_Duck_Brown_U
    400046: SA_Animals_Duck_Green
    400048: SA_Animals_Duck_White_U
    400050: SA_Animals_Rooster_Black
    400052: SA_Animals_Rooster_Brown_U
    400054: SA_Animals_Chicken_Brown
    400056: SA_Animals_Duck_Brown
    400058: SA_Animals_Duck_White
    400060: SA_Animals_Rooster_Brown
    2300000: SA_Animals_Chicken_Brown_U
    2300002: SA_Animals_Duck_Brown_U
    2300004: SA_Animals_Duck_White_U
    2300006: SA_Animals_Rooster_Brown_U
    3300000: SA_Animals_Chicken_Brown_U
    3300002: SA_Animals_Duck_Brown_U
    3300004: SA_Animals_Duck_White_U
    3300006: SA_Animals_Rooster_Brown_U
    4300000: SA_Animals_Chicken_Brown_U
    4300002: SA_Animals_Duck_Brown_U
    4300004: SA_Animals_Duck_White_U
    4300006: SA_Animals_Rooster_Brown_U
    4300008: SA_Animals_Chicken_White
    4300010: SA_Animals_Duck_Green
    4300012: SA_Animals_Rooster_Black
    4300014: SA_Animals_Rooster_Brown
    4300016: SA_Animals_Duck_White
    4300018: SA_Animals_Duck_Brown
    4300020: SA_Animals_Chicken_Brown
    7400000: Birds_Idle
    7400002: Birds_Walk
    7400004: Birds_Run
    7400006: Birds_Eat
    9500000: //RootNode
    13700000: SA_Animals_Chicken_White
    13700002: SA_Animals_Duck_Green
    13700004: SA_Animals_Rooster_Black
    13700006: SA_Animals_Chicken_Brown
    13700008: SA_Animals_Duck_Brown
    13700010: SA_Animals_Duck_White
    13700012: SA_Animals_Rooster_Brown
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    optimizeGameObjects: 0
    motionNodeName: 
    animationCompression: 1
    animationRotationError: .5
    animationPositionError: .5
    animationScaleError: .5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations:
    - serializedVersion: 16
      name: Birds_Idle
      takeName: Take 001
      firstFrame: 1
      lastFrame: 120
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: ChickenRig_SHJntGrp
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_l_Leg_HipSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_l_Leg_HipSHJnt/ChickenRig_l_Leg_KneeSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_l_Leg_HipSHJnt/ChickenRig_l_Leg_KneeSHJnt/ChickenRig_l_Leg_AnkleSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_l_Leg_HipSHJnt/ChickenRig_l_Leg_KneeSHJnt/ChickenRig_l_Leg_AnkleSHJnt/ChickenRig_l_Leg_BallSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_l_Leg_HipSHJnt/ChickenRig_l_Leg_KneeSHJnt/ChickenRig_l_Leg_AnkleSHJnt/ChickenRig_l_Leg_BallSHJnt/ChickenRig_l_Leg_ToeSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Neck_01SHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Neck_01SHJnt/ChickenRig_Neck_02SHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Neck_01SHJnt/ChickenRig_Neck_02SHJnt/ChickenRig_Neck_TopSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Neck_01SHJnt/ChickenRig_Neck_02SHJnt/ChickenRig_Neck_TopSHJnt/ChickenRig_Head_TopSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_r_Leg_HipSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_r_Leg_HipSHJnt/ChickenRig_r_Leg_KneeSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_r_Leg_HipSHJnt/ChickenRig_r_Leg_KneeSHJnt/ChickenRig_r_Leg_AnkleSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_r_Leg_HipSHJnt/ChickenRig_r_Leg_KneeSHJnt/ChickenRig_r_Leg_AnkleSHJnt/ChickenRig_r_Leg_BallSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_r_Leg_HipSHJnt/ChickenRig_r_Leg_KneeSHJnt/ChickenRig_r_Leg_AnkleSHJnt/ChickenRig_r_Leg_BallSHJnt/ChickenRig_r_Leg_ToeSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Tail_01SHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Tail_01SHJnt/ChickenRig_Tail_02SHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Tail_01SHJnt/ChickenRig_Tail_02SHJnt/ChickenRig_Tail_TopSHJnt
        weight: 1
      - path: SA_Animals_Chicken_Brown
        weight: 1
      - path: SA_Animals_Chicken_White
        weight: 1
      - path: SA_Animals_Duck_Brown
        weight: 1
      - path: SA_Animals_Duck_Green
        weight: 1
      - path: SA_Animals_Duck_White
        weight: 1
      - path: SA_Animals_Rooster_Black
        weight: 1
      - path: SA_Animals_Rooster_Brown
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
    - serializedVersion: 16
      name: Birds_Walk
      takeName: Take 001
      firstFrame: 130
      lastFrame: 160
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: ChickenRig_SHJntGrp
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_l_Leg_HipSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_l_Leg_HipSHJnt/ChickenRig_l_Leg_KneeSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_l_Leg_HipSHJnt/ChickenRig_l_Leg_KneeSHJnt/ChickenRig_l_Leg_AnkleSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_l_Leg_HipSHJnt/ChickenRig_l_Leg_KneeSHJnt/ChickenRig_l_Leg_AnkleSHJnt/ChickenRig_l_Leg_BallSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_l_Leg_HipSHJnt/ChickenRig_l_Leg_KneeSHJnt/ChickenRig_l_Leg_AnkleSHJnt/ChickenRig_l_Leg_BallSHJnt/ChickenRig_l_Leg_ToeSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Neck_01SHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Neck_01SHJnt/ChickenRig_Neck_02SHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Neck_01SHJnt/ChickenRig_Neck_02SHJnt/ChickenRig_Neck_TopSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Neck_01SHJnt/ChickenRig_Neck_02SHJnt/ChickenRig_Neck_TopSHJnt/ChickenRig_Head_TopSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_r_Leg_HipSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_r_Leg_HipSHJnt/ChickenRig_r_Leg_KneeSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_r_Leg_HipSHJnt/ChickenRig_r_Leg_KneeSHJnt/ChickenRig_r_Leg_AnkleSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_r_Leg_HipSHJnt/ChickenRig_r_Leg_KneeSHJnt/ChickenRig_r_Leg_AnkleSHJnt/ChickenRig_r_Leg_BallSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_r_Leg_HipSHJnt/ChickenRig_r_Leg_KneeSHJnt/ChickenRig_r_Leg_AnkleSHJnt/ChickenRig_r_Leg_BallSHJnt/ChickenRig_r_Leg_ToeSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Tail_01SHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Tail_01SHJnt/ChickenRig_Tail_02SHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Tail_01SHJnt/ChickenRig_Tail_02SHJnt/ChickenRig_Tail_TopSHJnt
        weight: 1
      - path: SA_Animals_Chicken_Brown
        weight: 1
      - path: SA_Animals_Chicken_White
        weight: 1
      - path: SA_Animals_Duck_Brown
        weight: 1
      - path: SA_Animals_Duck_Green
        weight: 1
      - path: SA_Animals_Duck_White
        weight: 1
      - path: SA_Animals_Rooster_Black
        weight: 1
      - path: SA_Animals_Rooster_Brown
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
    - serializedVersion: 16
      name: Birds_Run
      takeName: Take 001
      firstFrame: 170
      lastFrame: 187
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: ChickenRig_SHJntGrp
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_l_Leg_HipSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_l_Leg_HipSHJnt/ChickenRig_l_Leg_KneeSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_l_Leg_HipSHJnt/ChickenRig_l_Leg_KneeSHJnt/ChickenRig_l_Leg_AnkleSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_l_Leg_HipSHJnt/ChickenRig_l_Leg_KneeSHJnt/ChickenRig_l_Leg_AnkleSHJnt/ChickenRig_l_Leg_BallSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_l_Leg_HipSHJnt/ChickenRig_l_Leg_KneeSHJnt/ChickenRig_l_Leg_AnkleSHJnt/ChickenRig_l_Leg_BallSHJnt/ChickenRig_l_Leg_ToeSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Neck_01SHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Neck_01SHJnt/ChickenRig_Neck_02SHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Neck_01SHJnt/ChickenRig_Neck_02SHJnt/ChickenRig_Neck_TopSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Neck_01SHJnt/ChickenRig_Neck_02SHJnt/ChickenRig_Neck_TopSHJnt/ChickenRig_Head_TopSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_r_Leg_HipSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_r_Leg_HipSHJnt/ChickenRig_r_Leg_KneeSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_r_Leg_HipSHJnt/ChickenRig_r_Leg_KneeSHJnt/ChickenRig_r_Leg_AnkleSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_r_Leg_HipSHJnt/ChickenRig_r_Leg_KneeSHJnt/ChickenRig_r_Leg_AnkleSHJnt/ChickenRig_r_Leg_BallSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_r_Leg_HipSHJnt/ChickenRig_r_Leg_KneeSHJnt/ChickenRig_r_Leg_AnkleSHJnt/ChickenRig_r_Leg_BallSHJnt/ChickenRig_r_Leg_ToeSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Tail_01SHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Tail_01SHJnt/ChickenRig_Tail_02SHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Tail_01SHJnt/ChickenRig_Tail_02SHJnt/ChickenRig_Tail_TopSHJnt
        weight: 1
      - path: SA_Animals_Chicken_Brown
        weight: 1
      - path: SA_Animals_Chicken_White
        weight: 1
      - path: SA_Animals_Duck_Brown
        weight: 1
      - path: SA_Animals_Duck_Green
        weight: 1
      - path: SA_Animals_Duck_White
        weight: 1
      - path: SA_Animals_Rooster_Black
        weight: 1
      - path: SA_Animals_Rooster_Brown
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
    - serializedVersion: 16
      name: Birds_Eat
      takeName: Take 001
      firstFrame: 200
      lastFrame: 320
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: ChickenRig_SHJntGrp
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_l_Leg_HipSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_l_Leg_HipSHJnt/ChickenRig_l_Leg_KneeSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_l_Leg_HipSHJnt/ChickenRig_l_Leg_KneeSHJnt/ChickenRig_l_Leg_AnkleSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_l_Leg_HipSHJnt/ChickenRig_l_Leg_KneeSHJnt/ChickenRig_l_Leg_AnkleSHJnt/ChickenRig_l_Leg_BallSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_l_Leg_HipSHJnt/ChickenRig_l_Leg_KneeSHJnt/ChickenRig_l_Leg_AnkleSHJnt/ChickenRig_l_Leg_BallSHJnt/ChickenRig_l_Leg_ToeSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Neck_01SHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Neck_01SHJnt/ChickenRig_Neck_02SHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Neck_01SHJnt/ChickenRig_Neck_02SHJnt/ChickenRig_Neck_TopSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Neck_01SHJnt/ChickenRig_Neck_02SHJnt/ChickenRig_Neck_TopSHJnt/ChickenRig_Head_TopSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_r_Leg_HipSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_r_Leg_HipSHJnt/ChickenRig_r_Leg_KneeSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_r_Leg_HipSHJnt/ChickenRig_r_Leg_KneeSHJnt/ChickenRig_r_Leg_AnkleSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_r_Leg_HipSHJnt/ChickenRig_r_Leg_KneeSHJnt/ChickenRig_r_Leg_AnkleSHJnt/ChickenRig_r_Leg_BallSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_r_Leg_HipSHJnt/ChickenRig_r_Leg_KneeSHJnt/ChickenRig_r_Leg_AnkleSHJnt/ChickenRig_r_Leg_BallSHJnt/ChickenRig_r_Leg_ToeSHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Tail_01SHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Tail_01SHJnt/ChickenRig_Tail_02SHJnt
        weight: 1
      - path: ChickenRig_SHJntGrp/ChickenRig_ROOTSHJnt/ChickenRig_Tail_01SHJnt/ChickenRig_Tail_02SHJnt/ChickenRig_Tail_TopSHJnt
        weight: 1
      - path: SA_Animals_Chicken_Brown
        weight: 1
      - path: SA_Animals_Chicken_White
        weight: 1
      - path: SA_Animals_Duck_Brown
        weight: 1
      - path: SA_Animals_Duck_Green
        weight: 1
      - path: SA_Animals_Duck_White
        weight: 1
      - path: SA_Animals_Rooster_Black
        weight: 1
      - path: SA_Animals_Rooster_Brown
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 100
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 20
    splitTangentsAcrossUV: 0
    normalImportMode: 1
    tangentImportMode: 1
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human: []
    skeleton: []
    armTwist: .5
    foreArmTwist: .5
    upperLegTwist: .5
    legTwist: .5
    armStretch: .0500000007
    legStretch: .0500000007
    feetSpacing: 0
    rootMotionBoneName: 
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 2
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
