{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 6108, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 6108, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 6108, "tid": 6630, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 6108, "tid": 6630, "ts": 1752660781448496, "dur": 2104, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 6108, "tid": 6630, "ts": 1752660781460611, "dur": 1411, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 6108, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 6108, "tid": 1, "ts": 1752660780868745, "dur": 8454, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 6108, "tid": 1, "ts": 1752660780877203, "dur": 187744, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 6108, "tid": 1, "ts": 1752660781064955, "dur": 178848, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 6108, "tid": 6630, "ts": 1752660781462031, "dur": 23, "ph": "X", "name": "", "args": {}}, {"pid": 6108, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780866798, "dur": 12822, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780879623, "dur": 552873, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780880685, "dur": 2440, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780883133, "dur": 1356, "ph": "X", "name": "ProcessMessages 2301", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780884493, "dur": 244, "ph": "X", "name": "ReadAsync 2301", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780884741, "dur": 14, "ph": "X", "name": "ProcessMessages 20571", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780884756, "dur": 41, "ph": "X", "name": "ReadAsync 20571", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780884800, "dur": 2, "ph": "X", "name": "ProcessMessages 1579", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780884803, "dur": 29, "ph": "X", "name": "ReadAsync 1579", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780884836, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780884840, "dur": 30, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780884872, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780884873, "dur": 29, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780884904, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780884906, "dur": 31, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780884939, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780884941, "dur": 36, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780884978, "dur": 1, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780884980, "dur": 43, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885026, "dur": 1, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885028, "dur": 32, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885061, "dur": 1, "ph": "X", "name": "ProcessMessages 886", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885063, "dur": 34, "ph": "X", "name": "ReadAsync 886", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885100, "dur": 1, "ph": "X", "name": "ProcessMessages 820", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885102, "dur": 28, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885133, "dur": 28, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885163, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885165, "dur": 32, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885198, "dur": 1, "ph": "X", "name": "ProcessMessages 868", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885200, "dur": 31, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885233, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885235, "dur": 31, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885268, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885269, "dur": 29, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885301, "dur": 34, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885337, "dur": 1, "ph": "X", "name": "ProcessMessages 737", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885340, "dur": 38, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885381, "dur": 28, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885412, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885413, "dur": 29, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885444, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885446, "dur": 32, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885480, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885482, "dur": 32, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885517, "dur": 1, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885519, "dur": 32, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885553, "dur": 1, "ph": "X", "name": "ProcessMessages 812", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885556, "dur": 30, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885589, "dur": 1, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885590, "dur": 28, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885621, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885623, "dur": 31, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885657, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885659, "dur": 33, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885694, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885696, "dur": 27, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885724, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885726, "dur": 33, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885762, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885798, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885800, "dur": 29, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885833, "dur": 29, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885864, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885866, "dur": 30, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885897, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885899, "dur": 79, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780885981, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886012, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886014, "dur": 34, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886050, "dur": 1, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886052, "dur": 29, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886083, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886085, "dur": 28, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886117, "dur": 31, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886150, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886152, "dur": 29, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886183, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886184, "dur": 28, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886214, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886218, "dur": 25, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886247, "dur": 28, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886277, "dur": 1, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886278, "dur": 37, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886317, "dur": 1, "ph": "X", "name": "ProcessMessages 1020", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886319, "dur": 32, "ph": "X", "name": "ReadAsync 1020", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886356, "dur": 1, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886358, "dur": 37, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886398, "dur": 1, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886400, "dur": 38, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886440, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886443, "dur": 33, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886478, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886480, "dur": 33, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886518, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886520, "dur": 38, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886562, "dur": 118, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886684, "dur": 1, "ph": "X", "name": "ProcessMessages 201", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886687, "dur": 76, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886767, "dur": 3, "ph": "X", "name": "ProcessMessages 1871", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886771, "dur": 48, "ph": "X", "name": "ReadAsync 1871", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886822, "dur": 1, "ph": "X", "name": "ProcessMessages 916", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886826, "dur": 40, "ph": "X", "name": "ReadAsync 916", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886869, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780886872, "dur": 124, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887001, "dur": 1, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887003, "dur": 55, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887108, "dur": 2, "ph": "X", "name": "ProcessMessages 847", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887112, "dur": 66, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887182, "dur": 2, "ph": "X", "name": "ProcessMessages 1642", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887185, "dur": 38, "ph": "X", "name": "ReadAsync 1642", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887227, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887229, "dur": 57, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887291, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887337, "dur": 1, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887339, "dur": 111, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887454, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887457, "dur": 44, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887504, "dur": 1, "ph": "X", "name": "ProcessMessages 779", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887506, "dur": 49, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887557, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887559, "dur": 34, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887596, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887597, "dur": 51, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887653, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887655, "dur": 50, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887708, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887710, "dur": 41, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887753, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887755, "dur": 46, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887803, "dur": 1, "ph": "X", "name": "ProcessMessages 757", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887806, "dur": 59, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887869, "dur": 2, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887872, "dur": 40, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887914, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887917, "dur": 32, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887952, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780887955, "dur": 79, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780888037, "dur": 1, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780888040, "dur": 53, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780888097, "dur": 2, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780888100, "dur": 29, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780888132, "dur": 44, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780888178, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780888180, "dur": 47, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780888231, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780888235, "dur": 706, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780888945, "dur": 2, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780888949, "dur": 204, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889156, "dur": 6, "ph": "X", "name": "ProcessMessages 6557", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889164, "dur": 55, "ph": "X", "name": "ReadAsync 6557", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889224, "dur": 1, "ph": "X", "name": "ProcessMessages 823", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889227, "dur": 46, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889276, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889279, "dur": 31, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889312, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889314, "dur": 33, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889351, "dur": 23, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889379, "dur": 38, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889419, "dur": 1, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889422, "dur": 41, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889466, "dur": 2, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889469, "dur": 40, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889512, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889514, "dur": 32, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889548, "dur": 15, "ph": "X", "name": "ProcessMessages 130", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889564, "dur": 54, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889621, "dur": 2, "ph": "X", "name": "ProcessMessages 1383", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889624, "dur": 41, "ph": "X", "name": "ReadAsync 1383", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889669, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889677, "dur": 37, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889717, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889719, "dur": 43, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889765, "dur": 1, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889767, "dur": 34, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889803, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889804, "dur": 24, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889832, "dur": 26, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889860, "dur": 40, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889903, "dur": 1, "ph": "X", "name": "ProcessMessages 812", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889905, "dur": 46, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889955, "dur": 3, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889959, "dur": 34, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889995, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780889998, "dur": 42, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890043, "dur": 2, "ph": "X", "name": "ProcessMessages 1136", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890046, "dur": 59, "ph": "X", "name": "ReadAsync 1136", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890107, "dur": 1, "ph": "X", "name": "ProcessMessages 965", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890109, "dur": 39, "ph": "X", "name": "ReadAsync 965", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890150, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890152, "dur": 39, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890193, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890195, "dur": 36, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890234, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890236, "dur": 36, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890273, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890275, "dur": 28, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890306, "dur": 36, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890344, "dur": 1, "ph": "X", "name": "ProcessMessages 806", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890346, "dur": 30, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890379, "dur": 29, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890410, "dur": 2, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890414, "dur": 28, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890444, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890445, "dur": 30, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890479, "dur": 33, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890517, "dur": 2, "ph": "X", "name": "ProcessMessages 938", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890520, "dur": 32, "ph": "X", "name": "ReadAsync 938", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890554, "dur": 1, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890556, "dur": 26, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890586, "dur": 25, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890614, "dur": 33, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890649, "dur": 1, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890650, "dur": 35, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890688, "dur": 1, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890689, "dur": 25, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890718, "dur": 38, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890760, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890763, "dur": 52, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890820, "dur": 2, "ph": "X", "name": "ProcessMessages 1280", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890824, "dur": 46, "ph": "X", "name": "ReadAsync 1280", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890872, "dur": 9, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890882, "dur": 47, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890931, "dur": 1, "ph": "X", "name": "ProcessMessages 1390", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890934, "dur": 39, "ph": "X", "name": "ReadAsync 1390", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890975, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780890977, "dur": 33, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891014, "dur": 38, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891054, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891056, "dur": 41, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891099, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891101, "dur": 42, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891146, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891148, "dur": 44, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891194, "dur": 2, "ph": "X", "name": "ProcessMessages 1254", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891196, "dur": 39, "ph": "X", "name": "ReadAsync 1254", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891237, "dur": 1, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891240, "dur": 36, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891278, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891280, "dur": 39, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891321, "dur": 1, "ph": "X", "name": "ProcessMessages 846", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891323, "dur": 37, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891363, "dur": 1, "ph": "X", "name": "ProcessMessages 787", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891365, "dur": 38, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891405, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891407, "dur": 31, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891440, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891442, "dur": 47, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891491, "dur": 1, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891492, "dur": 32, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891526, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891528, "dur": 32, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891564, "dur": 36, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891602, "dur": 1, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891604, "dur": 46, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891653, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891655, "dur": 51, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891708, "dur": 1, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891711, "dur": 41, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891754, "dur": 1, "ph": "X", "name": "ProcessMessages 1282", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891756, "dur": 28, "ph": "X", "name": "ReadAsync 1282", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891787, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891789, "dur": 39, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891830, "dur": 1, "ph": "X", "name": "ProcessMessages 1118", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891832, "dur": 34, "ph": "X", "name": "ReadAsync 1118", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891868, "dur": 1, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891870, "dur": 30, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891902, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891904, "dur": 33, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891939, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891941, "dur": 25, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891967, "dur": 1, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780891969, "dur": 30, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892003, "dur": 28, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892033, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892035, "dur": 25, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892063, "dur": 34, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892099, "dur": 1, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892101, "dur": 53, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892156, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892157, "dur": 36, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892196, "dur": 1, "ph": "X", "name": "ProcessMessages 883", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892198, "dur": 28, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892229, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892282, "dur": 1, "ph": "X", "name": "ProcessMessages 1098", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892284, "dur": 33, "ph": "X", "name": "ReadAsync 1098", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892319, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892321, "dur": 21, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892345, "dur": 30, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892379, "dur": 25, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892408, "dur": 36, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892446, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892448, "dur": 40, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892490, "dur": 1, "ph": "X", "name": "ProcessMessages 1045", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892492, "dur": 29, "ph": "X", "name": "ReadAsync 1045", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892523, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892525, "dur": 31, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892558, "dur": 28, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892590, "dur": 24, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892617, "dur": 27, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892647, "dur": 49, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892700, "dur": 1, "ph": "X", "name": "ProcessMessages 926", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892703, "dur": 41, "ph": "X", "name": "ReadAsync 926", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892746, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892749, "dur": 37, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892794, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892797, "dur": 32, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892831, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892832, "dur": 45, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780892879, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893099, "dur": 66, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893167, "dur": 4, "ph": "X", "name": "ProcessMessages 5027", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893172, "dur": 40, "ph": "X", "name": "ReadAsync 5027", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893214, "dur": 1, "ph": "X", "name": "ProcessMessages 984", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893217, "dur": 30, "ph": "X", "name": "ReadAsync 984", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893249, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893251, "dur": 34, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893290, "dur": 1, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893292, "dur": 30, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893324, "dur": 1, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893326, "dur": 36, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893364, "dur": 1, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893365, "dur": 26, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893395, "dur": 31, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893430, "dur": 24, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893457, "dur": 33, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893493, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893519, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893521, "dur": 24, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893549, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893550, "dur": 32, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893588, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893590, "dur": 30, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893622, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893628, "dur": 34, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893664, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893666, "dur": 29, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893696, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893698, "dur": 32, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893732, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893734, "dur": 28, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893764, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893766, "dur": 43, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893811, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893813, "dur": 28, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893843, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893845, "dur": 26, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893875, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893904, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893906, "dur": 44, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893953, "dur": 1, "ph": "X", "name": "ProcessMessages 876", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893955, "dur": 40, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780893997, "dur": 1, "ph": "X", "name": "ProcessMessages 1011", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894000, "dur": 32, "ph": "X", "name": "ReadAsync 1011", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894034, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894036, "dur": 30, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894067, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894069, "dur": 32, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894103, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894104, "dur": 41, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894147, "dur": 1, "ph": "X", "name": "ProcessMessages 779", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894149, "dur": 26, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894179, "dur": 39, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894220, "dur": 1, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894222, "dur": 32, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894256, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894258, "dur": 27, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894288, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894289, "dur": 38, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894334, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894336, "dur": 38, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894376, "dur": 1, "ph": "X", "name": "ProcessMessages 932", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894378, "dur": 32, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894412, "dur": 1, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894413, "dur": 49, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894464, "dur": 1, "ph": "X", "name": "ProcessMessages 1166", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894465, "dur": 28, "ph": "X", "name": "ReadAsync 1166", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894496, "dur": 24, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894523, "dur": 22, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894548, "dur": 26, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894578, "dur": 22, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894603, "dur": 23, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894628, "dur": 24, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894655, "dur": 28, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894686, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894737, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894779, "dur": 1, "ph": "X", "name": "ProcessMessages 839", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894781, "dur": 30, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894813, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894815, "dur": 61, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894880, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894917, "dur": 1, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894919, "dur": 26, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780894949, "dur": 71, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895027, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895062, "dur": 1, "ph": "X", "name": "ProcessMessages 862", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895065, "dur": 26, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895094, "dur": 60, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895157, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895189, "dur": 30, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895221, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895223, "dur": 26, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895252, "dur": 64, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895319, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895359, "dur": 1, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895361, "dur": 25, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895390, "dur": 52, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895445, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895478, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895480, "dur": 27, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895509, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895511, "dur": 54, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895569, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895606, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895608, "dur": 32, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895643, "dur": 23, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895669, "dur": 51, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895724, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895757, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895759, "dur": 36, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895797, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895798, "dur": 43, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895844, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895894, "dur": 1, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895897, "dur": 27, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895928, "dur": 40, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780895971, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896011, "dur": 11, "ph": "X", "name": "ProcessMessages 927", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896024, "dur": 25, "ph": "X", "name": "ReadAsync 927", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896058, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896059, "dur": 31, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896095, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896127, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896128, "dur": 30, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896165, "dur": 24, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896194, "dur": 162, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896359, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896361, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896427, "dur": 3, "ph": "X", "name": "ProcessMessages 2451", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896432, "dur": 44, "ph": "X", "name": "ReadAsync 2451", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896480, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896522, "dur": 1, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896525, "dur": 27, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896557, "dur": 52, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896612, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896617, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896672, "dur": 1, "ph": "X", "name": "ProcessMessages 1080", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896674, "dur": 52, "ph": "X", "name": "ReadAsync 1080", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896731, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896769, "dur": 3, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896774, "dur": 26, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896805, "dur": 44, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896851, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896854, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896889, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896893, "dur": 27, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896923, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896925, "dur": 46, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780896975, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780897014, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780897015, "dur": 28, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780897045, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780897049, "dur": 53, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780897106, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780897150, "dur": 1, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780897154, "dur": 36, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780897192, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780897195, "dur": 29, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780897227, "dur": 2, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780897230, "dur": 54, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780897287, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780897336, "dur": 1, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780897338, "dur": 7903, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780905258, "dur": 9, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780905270, "dur": 8046, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780913382, "dur": 17, "ph": "X", "name": "ProcessMessages 20504", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780913423, "dur": 535, "ph": "X", "name": "ReadAsync 20504", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780913975, "dur": 65, "ph": "X", "name": "ProcessMessages 20483", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780914096, "dur": 52, "ph": "X", "name": "ReadAsync 20483", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780914188, "dur": 51, "ph": "X", "name": "ProcessMessages 1403", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780914373, "dur": 265, "ph": "X", "name": "ReadAsync 1403", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780914794, "dur": 43, "ph": "X", "name": "ProcessMessages 3712", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780914878, "dur": 94, "ph": "X", "name": "ReadAsync 3712", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780914981, "dur": 4, "ph": "X", "name": "ProcessMessages 5364", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780914986, "dur": 128, "ph": "X", "name": "ReadAsync 5364", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780915182, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780915274, "dur": 298, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780915579, "dur": 620, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780916242, "dur": 82, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780916351, "dur": 4, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780916410, "dur": 121, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780916534, "dur": 3, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780916538, "dur": 167, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780916737, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780916755, "dur": 27, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780916797, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780916800, "dur": 63, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780917207, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780917211, "dur": 200, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780917477, "dur": 65, "ph": "X", "name": "ProcessMessages 1600", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780917596, "dur": 480, "ph": "X", "name": "ReadAsync 1600", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780918218, "dur": 7, "ph": "X", "name": "ProcessMessages 1040", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780918420, "dur": 306, "ph": "X", "name": "ReadAsync 1040", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780918801, "dur": 104, "ph": "X", "name": "ProcessMessages 2236", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780918949, "dur": 395, "ph": "X", "name": "ReadAsync 2236", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780919382, "dur": 4, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780919388, "dur": 19040, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780938591, "dur": 52, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780938646, "dur": 906, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780939609, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780939659, "dur": 2083, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780941836, "dur": 43, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780941882, "dur": 352, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780942359, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780942441, "dur": 13453, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780955918, "dur": 70, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780956116, "dur": 514, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780956677, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780956804, "dur": 167, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780957016, "dur": 40, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780957058, "dur": 184, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780957287, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780957289, "dur": 230, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780957558, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780957596, "dur": 207, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780957846, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780957849, "dur": 97, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780957985, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780957988, "dur": 160, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780958236, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780958275, "dur": 265, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780958611, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780958614, "dur": 237, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780958948, "dur": 52, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780959086, "dur": 340, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780959478, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780959592, "dur": 255, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780959940, "dur": 54, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780959997, "dur": 297, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780960342, "dur": 29, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780960374, "dur": 194, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780960659, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780960664, "dur": 132, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780960983, "dur": 47, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780961033, "dur": 194, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780961270, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780961308, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780961352, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780961355, "dur": 41, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780961478, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780961480, "dur": 205, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780961737, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780961816, "dur": 285, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780962146, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780962180, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780962219, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780962284, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780962324, "dur": 162, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780962514, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780962515, "dur": 100, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780962617, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780962682, "dur": 105, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780962798, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780962800, "dur": 141, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780962944, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780962947, "dur": 111, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780963092, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780963094, "dur": 227, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780963477, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780963482, "dur": 145, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780963704, "dur": 44, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780963795, "dur": 295, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780964125, "dur": 1105, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780965236, "dur": 181, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780965550, "dur": 99, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780965753, "dur": 380, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780966272, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780966635, "dur": 463, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780967101, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780967104, "dur": 140, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780967349, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780967411, "dur": 127, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780967583, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660780967623, "dur": 62633, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781030341, "dur": 39, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781030383, "dur": 283, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781030672, "dur": 2352, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781033028, "dur": 6234, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781039356, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781039453, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781039529, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781039531, "dur": 160, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781039821, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781039824, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781039892, "dur": 347, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781040270, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781040272, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781040453, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781040456, "dur": 226, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781040685, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781040688, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781040765, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781040833, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781040899, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781040901, "dur": 814, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781041720, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781041783, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781041785, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781041850, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781041852, "dur": 135, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781041989, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781041991, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781042045, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781042109, "dur": 163, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781042276, "dur": 311, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781042701, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781042704, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781042734, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781042839, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781042896, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781042898, "dur": 329, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781043281, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781043284, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781043359, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781043361, "dur": 592, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781044048, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781044050, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781044079, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781044081, "dur": 300, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781044387, "dur": 180, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781044570, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781044572, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781044600, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781044602, "dur": 341, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781044947, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781044978, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781044980, "dur": 108, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781045091, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781045120, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781045265, "dur": 125, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781045425, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781045428, "dur": 831, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781046263, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781046292, "dur": 71, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781046365, "dur": 488, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781046857, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781046858, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781046887, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781046889, "dur": 139, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781047031, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781047033, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781047138, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781047141, "dur": 235, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781047456, "dur": 149, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781047644, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781047646, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781048042, "dur": 47, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781048092, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781048129, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781048131, "dur": 1054, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781049239, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781049241, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781049311, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781049313, "dur": 127, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781049509, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781049512, "dur": 109, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781049623, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781049626, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781049652, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781049722, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781049724, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781049834, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781049837, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781049911, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781049914, "dur": 317, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781050233, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781050236, "dur": 63, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781050302, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781050305, "dur": 24, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781050331, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781050333, "dur": 53, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781050389, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781050390, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781050552, "dur": 30, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781050585, "dur": 83, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781050670, "dur": 47, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781050719, "dur": 101, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781050823, "dur": 44, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781050869, "dur": 70, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781050943, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781050947, "dur": 70, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781051020, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781051023, "dur": 267, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781051294, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781051343, "dur": 103, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781051449, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781051451, "dur": 100, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781051779, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781051782, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781051817, "dur": 177293, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781229125, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781229129, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781229191, "dur": 31, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781229224, "dur": 66, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781229295, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781229300, "dur": 171, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781229475, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781229477, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781229512, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781229515, "dur": 135847, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781365374, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781365378, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781365434, "dur": 24, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781365460, "dur": 42, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781365504, "dur": 16, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781365523, "dur": 20984, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781386526, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781386544, "dur": 107, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781386657, "dur": 9, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781386668, "dur": 3029, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781389751, "dur": 15, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781389771, "dur": 154, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781389933, "dur": 5, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781389941, "dur": 876, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781390825, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781390836, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781390905, "dur": 64, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781390971, "dur": 40, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781391016, "dur": 16, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781391033, "dur": 23495, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781414545, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781414553, "dur": 182, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781414742, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781414746, "dur": 522, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781415273, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781415277, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781415319, "dur": 698, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660781416023, "dur": 15125, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 6108, "tid": 6630, "ts": 1752660781462055, "dur": 990, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 6108, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 6108, "tid": 8589934592, "ts": 1752660780863865, "dur": 379999, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 6108, "tid": 8589934592, "ts": 1752660781243868, "dur": 10, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 6108, "tid": 8589934592, "ts": 1752660781243880, "dur": 1451, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 6108, "tid": 6630, "ts": 1752660781463048, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 6108, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 6108, "tid": 4294967296, "ts": 1752660780781892, "dur": 652050, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 6108, "tid": 4294967296, "ts": 1752660780786288, "dur": 60879, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 6108, "tid": 4294967296, "ts": 1752660781434270, "dur": 9973, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 6108, "tid": 4294967296, "ts": 1752660781439510, "dur": 381, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 6108, "tid": 4294967296, "ts": 1752660781444545, "dur": 53, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 6108, "tid": 6630, "ts": 1752660781463055, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752660780877404, "dur": 62, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752660780877713, "dur": 3241, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752660780880970, "dur": 1364, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752660780882526, "dur": 112, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1752660780882639, "dur": 574, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752660780884447, "dur": 1436, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_87AA2ADA08DFF7E1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752660780886475, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_C5267BC5A847D8B9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752660780887859, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_4B16FAA5016376E3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752660780888288, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752660780889555, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1752660780890233, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_84A4293DBDD182DB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752660780900626, "dur": 7200, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1752660780910736, "dur": 4200, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752660780883244, "dur": 32889, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752660780916151, "dur": 499526, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752660781415682, "dur": 390, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752660781416075, "dur": 84, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752660781416288, "dur": 80, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752660781416397, "dur": 2307, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752660780883274, "dur": 32887, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780916194, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780916312, "dur": 858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_31AB2D3F1E4A3D66.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752660780917260, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_455623E8771FA1DB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752660780917458, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_29BCCE2A849A4ABC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752660780917544, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_1A6A88D66EF4C557.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752660780917698, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_C7086B172735D898.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752660780917826, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780918205, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752660780918341, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752660780918852, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752660780919070, "dur": 292, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1752660780919363, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780919456, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780919893, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752660780919978, "dur": 1466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780921445, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780922201, "dur": 1592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780923794, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780924437, "dur": 589, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\treeview\\Drawers\\TrackItemsDrawer.cs"}}, {"pid": 12345, "tid": 1, "ts": 1752660780924206, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780925213, "dur": 1771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780926984, "dur": 1605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780928590, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780928997, "dur": 1523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780931874, "dur": 745, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Implementation\\GraphObject.cs"}}, {"pid": 12345, "tid": 1, "ts": 1752660780930520, "dur": 2100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780932620, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780933052, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780933887, "dur": 1652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780935540, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780935965, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780936323, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780936737, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780938620, "dur": 1378, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Runtime\\GPUDriven\\GPUResidentBatcher.cs"}}, {"pid": 12345, "tid": 1, "ts": 1752660780937157, "dur": 4314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780941472, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780941924, "dur": 1773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780943698, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780944108, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780944516, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780944967, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780946462, "dur": 512, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@56bff8827a7e\\Unity.Collections\\NativeArrayExtensions.cs"}}, {"pid": 12345, "tid": 1, "ts": 1752660780945408, "dur": 2063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780947472, "dur": 1967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780949440, "dur": 1946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780951387, "dur": 1519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780952907, "dur": 1319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780954226, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780955040, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780956130, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780956808, "dur": 1271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780958080, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752660780958326, "dur": 4285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752660780962612, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780962877, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752660780963032, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780963146, "dur": 836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752660780963983, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660780964185, "dur": 73948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660781038135, "dur": 2275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752660781040411, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660781040500, "dur": 2446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752660781042947, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660781043074, "dur": 2196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752660781045271, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660781045357, "dur": 2389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752660781047746, "dur": 777, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660781048530, "dur": 2153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752660781051051, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660781051306, "dur": 403, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660781051877, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752660781052206, "dur": 514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752660781052761, "dur": 363018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752660780883627, "dur": 32796, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752660780916430, "dur": 773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B76B86F6FFE10D96.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752660780917690, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752660780917827, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_7FE145A456DFCBEF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752660780917896, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752660780918030, "dur": 777, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_7FE145A456DFCBEF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752660780918822, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752660780918952, "dur": 23811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752660780942765, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752660780942889, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752660780943033, "dur": 13608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752660780956644, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752660780956874, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752660780957044, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752660780957517, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752660780957648, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752660780957828, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752660780958387, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752660780958486, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752660780958696, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752660780959221, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752660780959435, "dur": 955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752660780960391, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752660780960455, "dur": 1325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752660780961781, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752660780961972, "dur": 1242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752660780963214, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752660780963298, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752660780963414, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752660780963651, "dur": 558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752660780964210, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752660780964442, "dur": 73696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752660781038144, "dur": 2220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752660781040365, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752660781040448, "dur": 2713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752660781043161, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752660781043227, "dur": 2358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752660781045586, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752660781045712, "dur": 2253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752660781047966, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752660781048019, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752660781048115, "dur": 2378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752660781050494, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752660781050720, "dur": 1952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752660781052757, "dur": 363048, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780883348, "dur": 32837, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780916191, "dur": 1124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_E7120EB995DA210C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752660780917672, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_C5267BC5A847D8B9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752660780918026, "dur": 581, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_BC1462186899290E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752660780918778, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752660780919340, "dur": 430, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780921057, "dur": 1192, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752660780919985, "dur": 2756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780923423, "dur": 560, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-string-l1-1-0.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752660780922742, "dur": 1990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780924733, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780925142, "dur": 1919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780927062, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780927893, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780928353, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780928759, "dur": 1918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780931861, "dur": 763, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Graphs\\SerializableVirtualTexture.cs"}}, {"pid": 12345, "tid": 3, "ts": 1752660780930677, "dur": 2873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780933550, "dur": 2730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780936280, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780936715, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780937292, "dur": 1554, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Runtime\\GPUDriven\\Utilities\\ParallelBitArray.cs"}}, {"pid": 12345, "tid": 3, "ts": 1752660780938847, "dur": 1174, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Runtime\\GPUDriven\\Utilities\\MemoryUtilities.cs"}}, {"pid": 12345, "tid": 3, "ts": 1752660780940434, "dur": 586, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Runtime\\GPUDriven\\OcclusionCullingDebugShaderVariables.cs"}}, {"pid": 12345, "tid": 3, "ts": 1752660780941020, "dur": 777, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Runtime\\GPUDriven\\OcclusionCullingCommonShaderVariables.cs"}}, {"pid": 12345, "tid": 3, "ts": 1752660780937130, "dur": 6233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780943363, "dur": 3161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780946525, "dur": 1852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780948377, "dur": 2178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780950556, "dur": 2161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780953620, "dur": 821, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Gluon\\ProgressOperationHandler.cs"}}, {"pid": 12345, "tid": 3, "ts": 1752660780954443, "dur": 1117, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Gluon\\IncomingChangesNotification.cs"}}, {"pid": 12345, "tid": 3, "ts": 1752660780952717, "dur": 3066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780955783, "dur": 1049, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780956832, "dur": 828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780957665, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752660780957837, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780957909, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752660780958405, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780958536, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752660780958856, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780958995, "dur": 1477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752660780960472, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780960587, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752660780960737, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780960842, "dur": 1880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752660780962722, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780962987, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752660780963646, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752660780963842, "dur": 1607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752660780965450, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780965624, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752660780965798, "dur": 860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752660780966660, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780966771, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752660780966915, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752660780967289, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780967357, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752660780968268, "dur": 64, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660780969248, "dur": 255035, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752660781227934, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752660781227592, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752660781228094, "dur": 1027, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752660781230388, "dur": 214, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752660781231264, "dur": 135378, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752660781390293, "dur": 240, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1752660781390238, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1752660781390655, "dur": 25079, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780883331, "dur": 32843, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780916195, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780916450, "dur": 816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_172A8F2500200F04.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752660780917267, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780917692, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780918146, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780918491, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752660780918591, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752660780919169, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752660780919735, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14832617886295879631.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752660780919954, "dur": 1305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780921419, "dur": 1307, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 4, "ts": 1752660780921261, "dur": 2578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780923840, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780924253, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780924677, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780925109, "dur": 1651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780926761, "dur": 1875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780928637, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780929225, "dur": 1487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780931868, "dur": 755, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Graphs\\Matrix2ShaderProperty.cs"}}, {"pid": 12345, "tid": 4, "ts": 1752660780930713, "dur": 2250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780932964, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780933381, "dur": 1723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780936386, "dur": 805, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Editor\\Lighting\\ProbeVolume\\ProbeAdjustmentVolumeEditor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1752660780935104, "dur": 2705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780937810, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780938286, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780938757, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780939188, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780939646, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780940068, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780940534, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780940993, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780941426, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780942625, "dur": 587, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Runtime\\Utilities\\DelegateUtility.cs"}}, {"pid": 12345, "tid": 4, "ts": 1752660780941850, "dur": 2482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780944333, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780944827, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780945319, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780946319, "dur": 1849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780948168, "dur": 2023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780950192, "dur": 2241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780952433, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780953311, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780954399, "dur": 989, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@dfdbd02f5918\\UnityEditor.TestRunner\\TestRun\\Tasks\\FileCleanupVerifierTaskBase.cs"}}, {"pid": 12345, "tid": 4, "ts": 1752660780954282, "dur": 1670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780955953, "dur": 848, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780956802, "dur": 1766, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780958569, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_621CDDF9C514DF8F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752660780958938, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752660780959112, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752660780959475, "dur": 611, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780960090, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752660780960263, "dur": 1247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752660780961511, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780961577, "dur": 732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752660780962310, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780962369, "dur": 1510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752660780963879, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780963982, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780964090, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752660780964335, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752660780964723, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660780964827, "dur": 73342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660781038174, "dur": 2325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752660781040500, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660781040624, "dur": 2116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752660781042741, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660781042824, "dur": 2476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752660781045300, "dur": 1009, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660781046323, "dur": 2275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752660781048599, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660781048658, "dur": 3800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752660781052459, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660781052565, "dur": 334874, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660781387519, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1752660781387448, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1752660781391938, "dur": 94, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752660781387670, "dur": 4391, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1752660781392070, "dur": 23629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780883385, "dur": 32811, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780916205, "dur": 1071, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_DD3CBC65A51B3B4A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752660780917636, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780918502, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752660780918851, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1752660780919723, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752660780919976, "dur": 1494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780921471, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780922389, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780923443, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780924584, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780925024, "dur": 1952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780926977, "dur": 1725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780928702, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780929100, "dur": 1682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780931843, "dur": 761, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a55da47cc43f\\Runtime\\2D\\Renderer2D.cs"}}, {"pid": 12345, "tid": 5, "ts": 1752660780930782, "dur": 2453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780933236, "dur": 1998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780936476, "dur": 707, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Editor\\CoreEditorStyles.cs"}}, {"pid": 12345, "tid": 5, "ts": 1752660780935234, "dur": 2657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780937892, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780938321, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780938787, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780939242, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780939660, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780940095, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780940523, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780940978, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780941418, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780941866, "dur": 1522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780943388, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780943916, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780944332, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780944729, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780945160, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780945601, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780945993, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780947336, "dur": 1713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780949050, "dur": 2268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780951318, "dur": 1529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780952848, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780954097, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780954538, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780954956, "dur": 967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780955924, "dur": 885, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780956810, "dur": 864, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780957675, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752660780957869, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752660780958431, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780958498, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752660780958706, "dur": 1134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752660780959841, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780959904, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780960142, "dur": 758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752660780960928, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752660780961449, "dur": 952, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780962437, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752660780963078, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780963199, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752660780963388, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752660780963802, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780963906, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660780964129, "dur": 74020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660781038150, "dur": 2106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752660781040257, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660781040349, "dur": 2544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752660781042893, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660781042993, "dur": 2629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752660781045676, "dur": 2199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752660781047876, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660781048052, "dur": 2176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752660781050228, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660781050300, "dur": 2193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752660781052567, "dur": 337686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660781390379, "dur": 25057, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1752660781390262, "dur": 25181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1752660781415449, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780883414, "dur": 32797, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780916222, "dur": 929, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_36D94D0B0882E7EC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752660780917352, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780917463, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780917645, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780917826, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_2AE61BEFE92D422E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752660780918031, "dur": 532, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_FFA66C83E76ECCA7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752660780918617, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780918683, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780918850, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1752660780919078, "dur": 436, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1752660780919572, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752660780919754, "dur": 373, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752660780920127, "dur": 1865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780922021, "dur": 696, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Server.Kestrel.Core.dll"}}, {"pid": 12345, "tid": 6, "ts": 1752660780921993, "dur": 2013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780924007, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780924442, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780924853, "dur": 1534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780926397, "dur": 1609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780928006, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780928425, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780928851, "dur": 1928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780931873, "dur": 860, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a55da47cc43f\\Runtime\\2D\\Shadows\\ShadowProvider\\Providers\\ShadowShape2DProvider_SpriteRenderer.cs"}}, {"pid": 12345, "tid": 6, "ts": 1752660780930779, "dur": 2395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780933174, "dur": 1536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780935021, "dur": 631, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Editor\\MenuManager.cs"}}, {"pid": 12345, "tid": 6, "ts": 1752660780934711, "dur": 2328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780938386, "dur": 1268, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.rendering.light-transport@9bd588f963c0\\Runtime\\UnifiedRayTracing\\Common\\GeometryPool\\GeometryPoolDefs.cs"}}, {"pid": 12345, "tid": 6, "ts": 1752660780937039, "dur": 3908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780940948, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780941423, "dur": 1448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780942932, "dur": 2442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780945375, "dur": 1241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780946617, "dur": 1682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780948299, "dur": 2415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780952274, "dur": 651, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\PendingChanges\\Changelists\\MoveToChangelistMenuBuilder.cs"}}, {"pid": 12345, "tid": 6, "ts": 1752660780950714, "dur": 3157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780954565, "dur": 1308, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@dfdbd02f5918\\UnityEditor.TestRunner\\UnityTestProtocol\\UnityTestProtocolStarter.cs"}}, {"pid": 12345, "tid": 6, "ts": 1752660780953921, "dur": 2083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780956004, "dur": 813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780956817, "dur": 838, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780958321, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752660780958722, "dur": 1089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752660780959811, "dur": 696, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780960575, "dur": 640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752660780961216, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780961313, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752660780961591, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780961694, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752660780962054, "dur": 2328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752660780964383, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780964476, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752660780964639, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780964708, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752660780965316, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780965429, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752660780965604, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752660780966114, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780966219, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752660780966362, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752660780966736, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660780966841, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752660780967030, "dur": 71127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660781038170, "dur": 3072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752660781041249, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660781041365, "dur": 2928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752660781044294, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660781044382, "dur": 3492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752660781047875, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660781047980, "dur": 2651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752660781050632, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660781051047, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660781051155, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660781051636, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660781051763, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660781052142, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660781052248, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660781052782, "dur": 363010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780883448, "dur": 32776, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780916234, "dur": 948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_B8BEB69DE53F9C41.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752660780917225, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_B8BEB69DE53F9C41.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752660780917482, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_A3DF7B62ADBC8E51.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752660780917671, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_7251D83D80DD0453.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752660780918529, "dur": 373, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752660780919482, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752660780919749, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12900333509587573630.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752660780919834, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752660780921308, "dur": 845, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a55da47cc43f\\Editor\\Converter\\PPv2\\EffectConverters\\ColorGradingConverter.cs"}}, {"pid": 12345, "tid": 7, "ts": 1752660780919952, "dur": 2412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780922365, "dur": 1568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780923934, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780924338, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780924778, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780925945, "dur": 1909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780927855, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780928313, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780928708, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780929376, "dur": 1537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780931823, "dur": 798, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a55da47cc43f\\Runtime\\2D\\CinemachineUniversalPixelPerfect.cs"}}, {"pid": 12345, "tid": 7, "ts": 1752660780930913, "dur": 2310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780933223, "dur": 1438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780934661, "dur": 1926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780936587, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780938369, "dur": 1479, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.rendering.light-transport@9bd588f963c0\\Runtime\\UnifiedRayTracing\\Hardware\\HardwareRayTracingBackend.cs"}}, {"pid": 12345, "tid": 7, "ts": 1752660780937034, "dur": 3983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780941018, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780941500, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780941907, "dur": 1819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780943727, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780944139, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780944546, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780945008, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780945504, "dur": 396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780945901, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780947134, "dur": 2489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780949624, "dur": 1730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780951354, "dur": 1602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780952956, "dur": 1623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780954580, "dur": 1007, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@dfdbd02f5918\\UnityEditor.TestRunner\\RequireApiProfileAttribute.cs"}}, {"pid": 12345, "tid": 7, "ts": 1752660780954580, "dur": 1496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780956077, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780956810, "dur": 858, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780957670, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752660780958213, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780958310, "dur": 278, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752660780958589, "dur": 765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752660780959354, "dur": 518, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780959934, "dur": 1401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752660780961336, "dur": 480, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780961820, "dur": 1190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1752660780963011, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780963971, "dur": 231, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660780964891, "dur": 66436, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1752660781038133, "dur": 2393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752660781040527, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660781040614, "dur": 1959, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752660781042574, "dur": 459, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660781043074, "dur": 2043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752660781045117, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660781045223, "dur": 2084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752660781047308, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660781047396, "dur": 2043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752660781049440, "dur": 916, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660781050363, "dur": 1939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752660781052393, "dur": 199345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752660781251740, "dur": 164010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780883484, "dur": 32800, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780916291, "dur": 1191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_01F0438719F4637C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752660780917684, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780918275, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752660780918506, "dur": 237, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752660780918862, "dur": 368, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1752660780919724, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752660780919862, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752660780919949, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752660780921294, "dur": 950, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.IO.Compression.Brotli.dll"}}, {"pid": 12345, "tid": 8, "ts": 1752660780920023, "dur": 2813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780922836, "dur": 1833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780924669, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780925116, "dur": 1810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780926926, "dur": 1572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780928499, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780928906, "dur": 1836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780931774, "dur": 839, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Graphs\\DynamicMatrixMaterialSlot.cs"}}, {"pid": 12345, "tid": 8, "ts": 1752660780930742, "dur": 2519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780933261, "dur": 1761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780936423, "dur": 884, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Editor\\Lighting\\ProbeVolume\\ProbeVolumeUI.Skin.cs"}}, {"pid": 12345, "tid": 8, "ts": 1752660780937601, "dur": 1331, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Editor\\Lighting\\ProbeVolume\\ProbeVolumeEditor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1752660780938932, "dur": 1140, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Editor\\Lighting\\ProbeVolume\\ProbeVolumeBuildProcessor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1752660780935023, "dur": 5592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780940616, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780941157, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780941578, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780942348, "dur": 769, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Runtime\\Debugging\\ProfilingScope.cs"}}, {"pid": 12345, "tid": 8, "ts": 1752660780942028, "dur": 3645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780946079, "dur": 852, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework.performance@92d1d09a72ed\\Runtime\\Metadata.cs"}}, {"pid": 12345, "tid": 8, "ts": 1752660780945673, "dur": 1258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780946931, "dur": 2434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780949366, "dur": 2207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780952820, "dur": 633, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\UI\\HandleMenuItem.cs"}}, {"pid": 12345, "tid": 8, "ts": 1752660780951574, "dur": 2080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780953655, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780954410, "dur": 1084, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@dfdbd02f5918\\UnityEditor.TestRunner\\TestRun\\Tasks\\BuildTestTreeTask.cs"}}, {"pid": 12345, "tid": 8, "ts": 1752660780954385, "dur": 1599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780955984, "dur": 841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780956826, "dur": 837, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780958570, "dur": 947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752660780959544, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752660780960033, "dur": 897, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780960943, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780961248, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752660780961456, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752660780961627, "dur": 659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752660780962287, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780962482, "dur": 1420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780963910, "dur": 1523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780965435, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752660780965596, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752660780965981, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660780966092, "dur": 72408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660781038912, "dur": 2367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752660781041280, "dur": 508, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660781041797, "dur": 2105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752660781043903, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660781043960, "dur": 2177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752660781046138, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660781046204, "dur": 2267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752660781048472, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660781048852, "dur": 2030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752660781050887, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660781051011, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660781051312, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660781051608, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660781051700, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660781051854, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660781051964, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660781052023, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660781052083, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660781052197, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752660781052584, "dur": 363084, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780883531, "dur": 32926, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780916459, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_B52235DE51A256C2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752660780917257, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780917463, "dur": 259, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_B52235DE51A256C2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752660780917723, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_AC0F081190478124.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752660780918517, "dur": 579, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752660780919202, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780919745, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752660780921010, "dur": 1627, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.IO.Pipes.AccessControl.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752660780919987, "dur": 2824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780922812, "dur": 1893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780924706, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780925094, "dur": 2050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780927145, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780927815, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780928419, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780928856, "dur": 1893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780931802, "dur": 771, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Attributes\\ContextFilterableAttribute.cs"}}, {"pid": 12345, "tid": 9, "ts": 1752660780930749, "dur": 2424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780933173, "dur": 2126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780936326, "dur": 1138, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Editor\\BuildProcessors\\SettingsStrippers\\GPUResidentDrawerResourcesStripper.cs"}}, {"pid": 12345, "tid": 9, "ts": 1752660780938340, "dur": 580, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Editor\\Analytics\\VolumePriorityUsageAnalytic.cs"}}, {"pid": 12345, "tid": 9, "ts": 1752660780938921, "dur": 1203, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Editor\\Analytics\\RenderPipelineGraphicsSettingsAnalytics.cs"}}, {"pid": 12345, "tid": 9, "ts": 1752660780935300, "dur": 5465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780940766, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780941509, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780942008, "dur": 3868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780945877, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780946845, "dur": 2219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780949065, "dur": 2233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780951299, "dur": 1967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780953266, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780954620, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780955086, "dur": 99, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780955329, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780955527, "dur": 1268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780956840, "dur": 811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780957657, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752660780957852, "dur": 1249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752660780959102, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780959331, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780959438, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752660780959699, "dur": 1684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752660780961384, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780961494, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752660780961641, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780961727, "dur": 533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752660780962261, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780962425, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752660780962653, "dur": 888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752660780963541, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780963643, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752660780963811, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752660780964387, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660780964502, "dur": 74012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660781038515, "dur": 2174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752660781040690, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660781040821, "dur": 2039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752660781042861, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660781042938, "dur": 7586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752660781050526, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752660781050597, "dur": 2090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752660781052756, "dur": 362908, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780883556, "dur": 32758, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780916320, "dur": 865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_F016717A46FE59BB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752660780917691, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_BBA4FF1108562044.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752660780918264, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780918495, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752660780918824, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752660780918964, "dur": 20317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752660780939283, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780939434, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780939904, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780940362, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780941302, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780941739, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780942540, "dur": 2064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780944604, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780945022, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780946034, "dur": 732, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ai.navigation@eb5635ad590d\\Editor\\NavigationOverlay.cs"}}, {"pid": 12345, "tid": 10, "ts": 1752660780945468, "dur": 1509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780946978, "dur": 2388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780949367, "dur": 2285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780951653, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780952853, "dur": 1326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780954179, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780954697, "dur": 937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780955634, "dur": 1175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780956809, "dur": 840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780957650, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752660780957845, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752660780958467, "dur": 518, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780959036, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752660780959219, "dur": 682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752660780959902, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780960033, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752660780960191, "dur": 910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752660780961101, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780961321, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780961402, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752660780961578, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752660780962150, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780962332, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752660780962518, "dur": 1019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752660780963538, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780963641, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752660780963828, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752660780964322, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660780964417, "dur": 73712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660781038132, "dur": 2081, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752660781040214, "dur": 1086, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660781041312, "dur": 3940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752660781045302, "dur": 2240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752660781047543, "dur": 464, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660781048012, "dur": 3499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752660781051512, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660781052068, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660781052183, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660781052578, "dur": 363236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780883584, "dur": 32743, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780916346, "dur": 885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_AC251BD94F5FD3AE.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752660780917689, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_657053D7FE219A28.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752660780918560, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752660780918852, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1752660780918965, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752660780919751, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752660780921076, "dur": 1514, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 11, "ts": 1752660780919956, "dur": 3074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780923031, "dur": 1988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780925019, "dur": 1510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780926530, "dur": 2184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780928714, "dur": 2400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780931875, "dur": 764, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@59eb6f11d242\\Editor\\BurstDisassembler.Core.LLVMIR.cs"}}, {"pid": 12345, "tid": 11, "ts": 1752660780931114, "dur": 1929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780933043, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780933493, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780935220, "dur": 1721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780936942, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780937425, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780937849, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780938582, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780939039, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780939555, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780940192, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780940763, "dur": 521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780941284, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780941740, "dur": 1957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780943698, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780944127, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780944538, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780944963, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780945414, "dur": 1419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780946834, "dur": 1761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780948595, "dur": 2239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780950834, "dur": 1872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780952706, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780953831, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780954143, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780954712, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780955223, "dur": 74, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780955324, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780955380, "dur": 77, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780955535, "dur": 1262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780956849, "dur": 804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780957654, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752660780957864, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752660780958359, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780958476, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752660780958660, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780958881, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752660780959068, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752660780959271, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752660780959811, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780959916, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752660780960139, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752660780960334, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752660780960788, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780960876, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752660780961301, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780961446, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752660780961592, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780961795, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752660780962323, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780962418, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780962551, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780962807, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780963071, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752660780963281, "dur": 719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752660780964000, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780964099, "dur": 2754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780966856, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752660780967041, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660780967099, "dur": 71045, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660781040483, "dur": 3091, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752660781043575, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660781043654, "dur": 2368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752660781046023, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660781046092, "dur": 2021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752660781048114, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660781048202, "dur": 2992, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752660781051242, "dur": 329, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752660781051719, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660781052160, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660781052389, "dur": 194098, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660781249832, "dur": 316, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 11, "ts": 1752660781250149, "dur": 1500, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 11, "ts": 1752660781251650, "dur": 69, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 11, "ts": 1752660781246490, "dur": 5238, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660781251729, "dur": 164039, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780883622, "dur": 32782, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780916418, "dur": 714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_59C0854D1BDEB3BB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752660780917265, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780917398, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_17831032FF1C6E20.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752660780917696, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_982F16DC875BE786.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752660780918178, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752660780918861, "dur": 328, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1752660780919477, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752660780919706, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752660780919958, "dur": 1489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780921448, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780922401, "dur": 1484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780923886, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780924308, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780924765, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780925193, "dur": 2175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780927369, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780927771, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780928192, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780928616, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780929070, "dur": 1507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780931726, "dur": 866, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Graphs\\Vector1MaterialSlot.cs"}}, {"pid": 12345, "tid": 12, "ts": 1752660780930577, "dur": 2603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780933180, "dur": 1480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780934660, "dur": 1797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780936457, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780936877, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780938172, "dur": 528, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Ports\\UnitPortCollection.cs"}}, {"pid": 12345, "tid": 12, "ts": 1752660780937306, "dur": 1657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780938963, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780939821, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780940354, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780941067, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780941494, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780941910, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780943153, "dur": 2392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780945545, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780945939, "dur": 1255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780947195, "dur": 2243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780949438, "dur": 2124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780951563, "dur": 1904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780954448, "dur": 848, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ai.navigation@eb5635ad590d\\Editor\\ConversionSystem\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 12, "ts": 1752660780953467, "dur": 2071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780955538, "dur": 1261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780956800, "dur": 858, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780957659, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752660780957860, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752660780958333, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780958472, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752660780958695, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752660780958836, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780958916, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752660780959074, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780959142, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752660780959852, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780959938, "dur": 1038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752660780960977, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780961069, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752660780961518, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780961876, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752660780962081, "dur": 866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752660780962947, "dur": 1328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660780964306, "dur": 73853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660781038161, "dur": 3711, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752660781041874, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660781041988, "dur": 2730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752660781044719, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660781045046, "dur": 2670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752660781047717, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660781047950, "dur": 2284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752660781050235, "dur": 636, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660781050898, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660781051112, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660781051484, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660781051609, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660781051666, "dur": 645, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660781052314, "dur": 175299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660781227675, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1752660781227626, "dur": 1387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752660781230007, "dur": 163, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660781231281, "dur": 135221, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752660781387435, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1752660781387412, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1752660781391941, "dur": 99, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752660781387630, "dur": 4439, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1752660781392073, "dur": 23600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752660781428900, "dur": 2684, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 6108, "tid": 6630, "ts": 1752660781463977, "dur": 9905, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 6108, "tid": 6630, "ts": 1752660781473986, "dur": 3345, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 6108, "tid": 6630, "ts": 1752660781458288, "dur": 20421, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}