{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 6108, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 6108, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 6108, "tid": 6586, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 6108, "tid": 6586, "ts": 1752660424972888, "dur": 1064, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 6108, "tid": 6586, "ts": 1752660424978445, "dur": 1005, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 6108, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 6108, "tid": 1, "ts": **********539824, "dur": 27006, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 6108, "tid": 1, "ts": **********566835, "dur": 105742, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 6108, "tid": 1, "ts": **********672587, "dur": 84160, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 6108, "tid": 6586, "ts": 1752660424979468, "dur": 25, "ph": "X", "name": "", "args": {}}, {"pid": 6108, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 6108, "tid": 12884901888, "ts": **********537552, "dur": 10630, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********548185, "dur": 7412028, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********549211, "dur": 2800, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********552019, "dur": 1165, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********553189, "dur": 6113, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********559316, "dur": 234, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********559554, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********559603, "dur": 765, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********560373, "dur": 10036, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********570420, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********570424, "dur": 736, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********571166, "dur": 591, "ph": "X", "name": "ProcessMessages 2506", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********571761, "dur": 252, "ph": "X", "name": "ReadAsync 2506", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572016, "dur": 14, "ph": "X", "name": "ProcessMessages 20494", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572031, "dur": 51, "ph": "X", "name": "ReadAsync 20494", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572084, "dur": 2, "ph": "X", "name": "ProcessMessages 1436", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572087, "dur": 33, "ph": "X", "name": "ReadAsync 1436", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572126, "dur": 31, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572159, "dur": 1, "ph": "X", "name": "ProcessMessages 913", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572161, "dur": 29, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572192, "dur": 1, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572194, "dur": 38, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572234, "dur": 1, "ph": "X", "name": "ProcessMessages 1027", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572238, "dur": 29, "ph": "X", "name": "ReadAsync 1027", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572269, "dur": 1, "ph": "X", "name": "ProcessMessages 806", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572270, "dur": 29, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572301, "dur": 1, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572302, "dur": 29, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572335, "dur": 27, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572364, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572365, "dur": 29, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572396, "dur": 1, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572398, "dur": 31, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572430, "dur": 1, "ph": "X", "name": "ProcessMessages 913", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572432, "dur": 27, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572463, "dur": 29, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572494, "dur": 1, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572495, "dur": 30, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572529, "dur": 28, "ph": "X", "name": "ReadAsync 835", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572559, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572561, "dur": 30, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572593, "dur": 1, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572594, "dur": 30, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572627, "dur": 31, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572660, "dur": 1, "ph": "X", "name": "ProcessMessages 1085", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572662, "dur": 28, "ph": "X", "name": "ReadAsync 1085", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572692, "dur": 1, "ph": "X", "name": "ProcessMessages 863", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572694, "dur": 35, "ph": "X", "name": "ReadAsync 863", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572731, "dur": 1, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572733, "dur": 27, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572762, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572763, "dur": 27, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572794, "dur": 23, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572819, "dur": 39, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572861, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572863, "dur": 27, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572893, "dur": 27, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572923, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572954, "dur": 1, "ph": "X", "name": "ProcessMessages 842", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572956, "dur": 34, "ph": "X", "name": "ReadAsync 842", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572992, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********572993, "dur": 29, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573024, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573025, "dur": 26, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573054, "dur": 85, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573143, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573145, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573197, "dur": 1, "ph": "X", "name": "ProcessMessages 1029", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573199, "dur": 59, "ph": "X", "name": "ReadAsync 1029", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573262, "dur": 2, "ph": "X", "name": "ProcessMessages 812", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573265, "dur": 39, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573306, "dur": 1, "ph": "X", "name": "ProcessMessages 866", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573309, "dur": 47, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573359, "dur": 1, "ph": "X", "name": "ProcessMessages 833", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573361, "dur": 36, "ph": "X", "name": "ReadAsync 833", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573400, "dur": 33, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573437, "dur": 32, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573472, "dur": 26, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573502, "dur": 22, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573527, "dur": 23, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573552, "dur": 28, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573582, "dur": 22, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573607, "dur": 23, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573632, "dur": 17, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573652, "dur": 27, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573682, "dur": 26, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573710, "dur": 25, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573738, "dur": 18, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573758, "dur": 25, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573786, "dur": 24, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573812, "dur": 29, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573844, "dur": 23, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573869, "dur": 29, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573901, "dur": 27, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573930, "dur": 27, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573960, "dur": 25, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********573988, "dur": 26, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574016, "dur": 28, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574046, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574047, "dur": 28, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574078, "dur": 25, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574105, "dur": 27, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574135, "dur": 19, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574157, "dur": 20, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574180, "dur": 18, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574201, "dur": 22, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574226, "dur": 31, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574260, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574262, "dur": 36, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574301, "dur": 1, "ph": "X", "name": "ProcessMessages 847", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574303, "dur": 32, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574337, "dur": 1, "ph": "X", "name": "ProcessMessages 869", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574339, "dur": 24, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574366, "dur": 19, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574388, "dur": 21, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574412, "dur": 32, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574446, "dur": 1, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574447, "dur": 36, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574485, "dur": 1, "ph": "X", "name": "ProcessMessages 1098", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574487, "dur": 22, "ph": "X", "name": "ReadAsync 1098", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574512, "dur": 19, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574534, "dur": 22, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574558, "dur": 21, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574581, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574583, "dur": 34, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574620, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574622, "dur": 28, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574653, "dur": 22, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574677, "dur": 74, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574754, "dur": 1, "ph": "X", "name": "ProcessMessages 1379", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574756, "dur": 25, "ph": "X", "name": "ReadAsync 1379", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574782, "dur": 8, "ph": "X", "name": "ProcessMessages 890", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574791, "dur": 30, "ph": "X", "name": "ReadAsync 890", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574823, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574824, "dur": 19, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574846, "dur": 19, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574867, "dur": 18, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574887, "dur": 26, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574916, "dur": 27, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574945, "dur": 1, "ph": "X", "name": "ProcessMessages 1018", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574947, "dur": 24, "ph": "X", "name": "ReadAsync 1018", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574974, "dur": 18, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********574994, "dur": 173, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575172, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575214, "dur": 1, "ph": "X", "name": "ProcessMessages 1358", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575216, "dur": 26, "ph": "X", "name": "ReadAsync 1358", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575244, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575256, "dur": 32, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575290, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575291, "dur": 30, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575323, "dur": 1, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575326, "dur": 32, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575360, "dur": 1, "ph": "X", "name": "ProcessMessages 823", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575362, "dur": 27, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575391, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575392, "dur": 27, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575422, "dur": 32, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575456, "dur": 1, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575458, "dur": 27, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575487, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575488, "dur": 28, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575518, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575520, "dur": 22, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575545, "dur": 29, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575576, "dur": 1, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575578, "dur": 38, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575618, "dur": 1, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575620, "dur": 30, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575651, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575653, "dur": 29, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575684, "dur": 1, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575686, "dur": 32, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575720, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575722, "dur": 34, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575759, "dur": 1, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575760, "dur": 29, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575793, "dur": 32, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575827, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575829, "dur": 32, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575862, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575864, "dur": 28, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575894, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575896, "dur": 23, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575922, "dur": 30, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575954, "dur": 1, "ph": "X", "name": "ProcessMessages 1000", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575956, "dur": 26, "ph": "X", "name": "ReadAsync 1000", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575984, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********575986, "dur": 29, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576016, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576019, "dur": 23, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576046, "dur": 32, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576080, "dur": 1, "ph": "X", "name": "ProcessMessages 1055", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576082, "dur": 29, "ph": "X", "name": "ReadAsync 1055", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576113, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576115, "dur": 29, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576147, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576149, "dur": 27, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576180, "dur": 29, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576211, "dur": 1, "ph": "X", "name": "ProcessMessages 773", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576212, "dur": 27, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576242, "dur": 25, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576268, "dur": 1, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576270, "dur": 24, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576297, "dur": 26, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576325, "dur": 35, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576363, "dur": 23, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576389, "dur": 22, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576413, "dur": 33, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576448, "dur": 1, "ph": "X", "name": "ProcessMessages 1125", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576449, "dur": 26, "ph": "X", "name": "ReadAsync 1125", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576478, "dur": 31, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576512, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576514, "dur": 35, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576551, "dur": 1, "ph": "X", "name": "ProcessMessages 989", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576553, "dur": 31, "ph": "X", "name": "ReadAsync 989", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576586, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576588, "dur": 28, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576618, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576619, "dur": 47, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576670, "dur": 14, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576685, "dur": 58, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576745, "dur": 2, "ph": "X", "name": "ProcessMessages 1209", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576748, "dur": 39, "ph": "X", "name": "ReadAsync 1209", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576790, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576792, "dur": 40, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576835, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576837, "dur": 43, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576883, "dur": 1, "ph": "X", "name": "ProcessMessages 992", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576885, "dur": 35, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576923, "dur": 1, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576925, "dur": 25, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576955, "dur": 32, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576989, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********576991, "dur": 27, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577021, "dur": 30, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577053, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577054, "dur": 34, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577089, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577091, "dur": 26, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577120, "dur": 33, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577156, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577158, "dur": 29, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577189, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577190, "dur": 37, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577230, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577231, "dur": 25, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577259, "dur": 26, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577288, "dur": 32, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577329, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577331, "dur": 31, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577364, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577365, "dur": 35, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577402, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577404, "dur": 28, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577435, "dur": 28, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577464, "dur": 1, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577466, "dur": 29, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577497, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577499, "dur": 26, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577528, "dur": 27, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577559, "dur": 28, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577589, "dur": 1, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577590, "dur": 30, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577623, "dur": 1, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577624, "dur": 26, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577653, "dur": 25, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577681, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577684, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577715, "dur": 28, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577747, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577789, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577791, "dur": 40, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577833, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577835, "dur": 59, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577896, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577898, "dur": 32, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577933, "dur": 39, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577974, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********577975, "dur": 32, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578009, "dur": 1, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578011, "dur": 34, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578048, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578050, "dur": 26, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578079, "dur": 36, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578118, "dur": 36, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578156, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578157, "dur": 26, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578185, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578187, "dur": 31, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578219, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578221, "dur": 62, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578286, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578288, "dur": 44, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578334, "dur": 1, "ph": "X", "name": "ProcessMessages 1183", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578337, "dur": 32, "ph": "X", "name": "ReadAsync 1183", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578372, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578373, "dur": 44, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578419, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578420, "dur": 30, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578452, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578453, "dur": 27, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578483, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578484, "dur": 30, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578516, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578518, "dur": 26, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578547, "dur": 236, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578786, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578787, "dur": 48, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********578839, "dur": 2, "ph": "X", "name": "ProcessMessages 2200", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********579544, "dur": 182, "ph": "X", "name": "ReadAsync 2200", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********579728, "dur": 10, "ph": "X", "name": "ProcessMessages 20566", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********579739, "dur": 33, "ph": "X", "name": "ReadAsync 20566", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********579774, "dur": 1, "ph": "X", "name": "ProcessMessages 1025", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********579776, "dur": 23, "ph": "X", "name": "ReadAsync 1025", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********579803, "dur": 23, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********579830, "dur": 21, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********579854, "dur": 26, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********579883, "dur": 24, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********579910, "dur": 30, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********579942, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********579943, "dur": 27, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********579973, "dur": 24, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580000, "dur": 27, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580030, "dur": 23, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580056, "dur": 25, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580084, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580086, "dur": 44, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580132, "dur": 1, "ph": "X", "name": "ProcessMessages 858", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580134, "dur": 28, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580164, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580166, "dur": 34, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580204, "dur": 24, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580230, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580232, "dur": 25, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580259, "dur": 21, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580284, "dur": 27, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580314, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580342, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580344, "dur": 37, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580383, "dur": 1, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580385, "dur": 69, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580458, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580494, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580497, "dur": 25, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580525, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580527, "dur": 85, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580615, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580650, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580652, "dur": 28, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580682, "dur": 71, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580756, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580787, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580789, "dur": 41, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580833, "dur": 34, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580872, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580908, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580910, "dur": 29, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580942, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********580944, "dur": 59, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581006, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581039, "dur": 28, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581070, "dur": 53, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581127, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581162, "dur": 34, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581197, "dur": 1, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581199, "dur": 23, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581225, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581280, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581313, "dur": 26, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581342, "dur": 54, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581401, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581438, "dur": 1, "ph": "X", "name": "ProcessMessages 926", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581440, "dur": 23, "ph": "X", "name": "ReadAsync 926", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581466, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581468, "dur": 60, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581531, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581567, "dur": 24, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581594, "dur": 57, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581655, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581688, "dur": 28, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581720, "dur": 25, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581747, "dur": 48, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********581799, "dur": 219, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582021, "dur": 1, "ph": "X", "name": "ProcessMessages 872", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582023, "dur": 47, "ph": "X", "name": "ReadAsync 872", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582072, "dur": 1, "ph": "X", "name": "ProcessMessages 1579", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582074, "dur": 36, "ph": "X", "name": "ReadAsync 1579", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582113, "dur": 22, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582137, "dur": 73, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582212, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582254, "dur": 25, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582282, "dur": 58, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582344, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582380, "dur": 1, "ph": "X", "name": "ProcessMessages 1038", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582382, "dur": 48, "ph": "X", "name": "ReadAsync 1038", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582433, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582471, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582473, "dur": 24, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582500, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582502, "dur": 53, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582558, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582591, "dur": 26, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582620, "dur": 59, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582682, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582730, "dur": 2, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582734, "dur": 36, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582771, "dur": 1, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582774, "dur": 51, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582828, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582863, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582865, "dur": 39, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582906, "dur": 1, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582908, "dur": 48, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582959, "dur": 1, "ph": "X", "name": "ProcessMessages 870", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582961, "dur": 25, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********582989, "dur": 23, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583015, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583073, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583112, "dur": 1, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583114, "dur": 27, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583143, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583144, "dur": 36, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583184, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583186, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583221, "dur": 1, "ph": "X", "name": "ProcessMessages 840", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583223, "dur": 37, "ph": "X", "name": "ReadAsync 840", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583263, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583265, "dur": 30, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583298, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583300, "dur": 29, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583331, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583333, "dur": 28, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583365, "dur": 37, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583405, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583408, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583441, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583442, "dur": 29, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583473, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583475, "dur": 54, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583533, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583568, "dur": 1, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583570, "dur": 31, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583603, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583606, "dur": 48, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583656, "dur": 1, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583658, "dur": 40, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583700, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583702, "dur": 49, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583758, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583788, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583790, "dur": 30, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583822, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583824, "dur": 44, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583871, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583907, "dur": 1, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583909, "dur": 26, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583937, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583939, "dur": 38, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********583981, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584015, "dur": 1, "ph": "X", "name": "ProcessMessages 820", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584017, "dur": 32, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584052, "dur": 1, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584054, "dur": 30, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584086, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584088, "dur": 30, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584120, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584122, "dur": 26, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584150, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584152, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584206, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584237, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584239, "dur": 29, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584270, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584272, "dur": 45, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584321, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584369, "dur": 29, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584401, "dur": 24, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584427, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584471, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584509, "dur": 1, "ph": "X", "name": "ProcessMessages 850", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584511, "dur": 28, "ph": "X", "name": "ReadAsync 850", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584541, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584543, "dur": 40, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584586, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584619, "dur": 1, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584622, "dur": 30, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584654, "dur": 1, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584656, "dur": 56, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584714, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584742, "dur": 24, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584769, "dur": 48, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584821, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584858, "dur": 1, "ph": "X", "name": "ProcessMessages 863", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584861, "dur": 32, "ph": "X", "name": "ReadAsync 863", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584895, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584897, "dur": 38, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584938, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584976, "dur": 1, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********584978, "dur": 27, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585007, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585009, "dur": 44, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585056, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585093, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585095, "dur": 32, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585129, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585131, "dur": 39, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585175, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585213, "dur": 1, "ph": "X", "name": "ProcessMessages 953", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585216, "dur": 25, "ph": "X", "name": "ReadAsync 953", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585242, "dur": 1, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585244, "dur": 23, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585270, "dur": 36, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585311, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585347, "dur": 1, "ph": "X", "name": "ProcessMessages 909", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585349, "dur": 25, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585377, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585379, "dur": 41, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585424, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585458, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585460, "dur": 32, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585495, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585497, "dur": 50, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585550, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585591, "dur": 1, "ph": "X", "name": "ProcessMessages 1012", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585593, "dur": 24, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585621, "dur": 44, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585668, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585707, "dur": 1, "ph": "X", "name": "ProcessMessages 901", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585709, "dur": 26, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585737, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585739, "dur": 42, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585787, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585823, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585825, "dur": 34, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585861, "dur": 9, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585872, "dur": 30, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585905, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585941, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585943, "dur": 28, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585973, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********585976, "dur": 48, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586028, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586063, "dur": 1, "ph": "X", "name": "ProcessMessages 889", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586065, "dur": 25, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586093, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586095, "dur": 52, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586150, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586187, "dur": 1, "ph": "X", "name": "ProcessMessages 1192", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586189, "dur": 46, "ph": "X", "name": "ReadAsync 1192", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586238, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586272, "dur": 24, "ph": "X", "name": "ReadAsync 997", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586298, "dur": 51, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586353, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586390, "dur": 1, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586392, "dur": 30, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586424, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586426, "dur": 52, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586481, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586520, "dur": 1, "ph": "X", "name": "ProcessMessages 961", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586522, "dur": 25, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586549, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586551, "dur": 41, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586595, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586630, "dur": 1, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586632, "dur": 27, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586661, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586663, "dur": 56, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586722, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586757, "dur": 1, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586759, "dur": 27, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586789, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586791, "dur": 40, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586834, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586864, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586866, "dur": 32, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586900, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586902, "dur": 60, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********586966, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587003, "dur": 1, "ph": "X", "name": "ProcessMessages 932", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587005, "dur": 26, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587033, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587034, "dur": 46, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587083, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587125, "dur": 1, "ph": "X", "name": "ProcessMessages 813", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587128, "dur": 36, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587166, "dur": 1, "ph": "X", "name": "ProcessMessages 869", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587169, "dur": 38, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587209, "dur": 1, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587211, "dur": 28, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587242, "dur": 26, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587271, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587283, "dur": 31, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587318, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587355, "dur": 1, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587357, "dur": 35, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587394, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587395, "dur": 103, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587502, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587504, "dur": 36, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587542, "dur": 1, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587543, "dur": 32, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587577, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587578, "dur": 34, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587615, "dur": 1, "ph": "X", "name": "ProcessMessages 998", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587616, "dur": 36, "ph": "X", "name": "ReadAsync 998", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587655, "dur": 1, "ph": "X", "name": "ProcessMessages 905", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587657, "dur": 30, "ph": "X", "name": "ReadAsync 905", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587690, "dur": 101, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587796, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587798, "dur": 37, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587837, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********587839, "dur": 183, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********588032, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********588094, "dur": 314, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********588411, "dur": 572, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********588986, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********588988, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********589034, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********589036, "dur": 911, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********589955, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********589958, "dur": 99, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590062, "dur": 7, "ph": "X", "name": "ProcessMessages 1072", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590070, "dur": 90, "ph": "X", "name": "ReadAsync 1072", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590163, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590166, "dur": 65, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590234, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590237, "dur": 29, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590270, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590273, "dur": 30, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590307, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590310, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590341, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590343, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590372, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590374, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590402, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590405, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590435, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590438, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590464, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590466, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590497, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590500, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590530, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590532, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590560, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590562, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590590, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590592, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590619, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590621, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590647, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590648, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590685, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590688, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590719, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590721, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590751, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590753, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590786, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590788, "dur": 36, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590827, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590829, "dur": 31, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590863, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590866, "dur": 28, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590897, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590900, "dur": 50, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590952, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590955, "dur": 25, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590982, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********590984, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591021, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591023, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591059, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591062, "dur": 29, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591094, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591096, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591134, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591137, "dur": 27, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591166, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591168, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591194, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591196, "dur": 26, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591224, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591227, "dur": 30, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591260, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591262, "dur": 41, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591307, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591309, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591344, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591347, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591375, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591377, "dur": 29, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591410, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591412, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591440, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591444, "dur": 26, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591473, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591475, "dur": 39, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591517, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591519, "dur": 25, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591547, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591549, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591579, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591581, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591608, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591611, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591659, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591662, "dur": 25, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591690, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591693, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591723, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591725, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591755, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591757, "dur": 27, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591788, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591816, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591817, "dur": 75, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591896, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591898, "dur": 61, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591962, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591964, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********591998, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592000, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592030, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592032, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592069, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592071, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592099, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592100, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592132, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592135, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592172, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592174, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592213, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592215, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592248, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592252, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592285, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592288, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592321, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592323, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592355, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592358, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592387, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592389, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592411, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592449, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592491, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592493, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592599, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592630, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********592632, "dur": 28449, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********621093, "dur": 12, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********621107, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********621378, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********621382, "dur": 7796, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********629214, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********629219, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********629462, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********629466, "dur": 57, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********629539, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********629542, "dur": 1017, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********630567, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********630569, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********630595, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********630597, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********630619, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********630684, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********630690, "dur": 289, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********630997, "dur": 228, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********631228, "dur": 9, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********631251, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********631292, "dur": 9, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********631302, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********631321, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********631399, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********631402, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********631450, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********631466, "dur": 91, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********631558, "dur": 11, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********631571, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********631623, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********631737, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********631765, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********631807, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********631914, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********631928, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********631955, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********631995, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********631997, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********632033, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********632035, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********632068, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********632070, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********632145, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********632147, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********632176, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********632178, "dur": 133, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********632320, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********632361, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********632363, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********632420, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********632423, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********632453, "dur": 265, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********632724, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********632760, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********632762, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********632803, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********632806, "dur": 159, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********632974, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********632976, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********633003, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********633005, "dur": 213, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********633225, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********633227, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********633273, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********633277, "dur": 47, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********633342, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********633372, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********633374, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********633415, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********633440, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********633471, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********633504, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********633543, "dur": 444, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********633996, "dur": 14, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********634012, "dur": 31, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********634046, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********634100, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********634102, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********634129, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********634132, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********634165, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********634168, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********634188, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********634262, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********634264, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********634305, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********634342, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********634523, "dur": 156, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********634685, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********634730, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********634732, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********634765, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********634767, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********634985, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********634988, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635030, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635033, "dur": 26, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635065, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635067, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635123, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635125, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635185, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635214, "dur": 54, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635283, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635286, "dur": 100, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635391, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635395, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635428, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635431, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635466, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635534, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635537, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635580, "dur": 14, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635596, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635718, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635720, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635753, "dur": 23, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635779, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635819, "dur": 12, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635832, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635867, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635985, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********635987, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********636009, "dur": 180, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********636201, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********636203, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********636239, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********636242, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********636269, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********636308, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********636346, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********636350, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********636392, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********636394, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********636476, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********636478, "dur": 102, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********636585, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********636587, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********636625, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********636675, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********636678, "dur": 158, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********636839, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********636852, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********636916, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********636918, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********636956, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********637010, "dur": 869, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********637886, "dur": 303, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********638193, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********638198, "dur": 48, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********638249, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********638251, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********638285, "dur": 134, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********638422, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********638474, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********638477, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********638524, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********638650, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********638653, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********638702, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********638704, "dur": 229, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********638947, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********638949, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********638977, "dur": 87, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********639069, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********639074, "dur": 301, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********639384, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********639391, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********639444, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********639531, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********639584, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********639588, "dur": 174, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********639766, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********639775, "dur": 167, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********639947, "dur": 129, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********640086, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********640099, "dur": 276, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********640381, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********640409, "dur": 1016, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********641429, "dur": 24, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********641455, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********641508, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********641529, "dur": 62141, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********703677, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********703682, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********703738, "dur": 2794, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********706537, "dur": 7053, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********713598, "dur": 58, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********713659, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********713708, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********713710, "dur": 155, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********713869, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********713872, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********713915, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********713918, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********713984, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********714003, "dur": 1821, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********715828, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********715830, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********715874, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********715877, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********715916, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********715918, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********715982, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********715984, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********716017, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********716100, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********716102, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********716133, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********716157, "dur": 147, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********716308, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********716336, "dur": 455, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********716794, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********716797, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********716841, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********716843, "dur": 857, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********717704, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********717705, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********717755, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********717757, "dur": 297, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********718060, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********718097, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********718099, "dur": 532, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********718636, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********718680, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********718682, "dur": 287, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********718972, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********718974, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********719029, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********719031, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********719078, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********719109, "dur": 201, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********719314, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********719341, "dur": 273, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********719619, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********719649, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********719651, "dur": 758, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********720414, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********720447, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********720449, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********720479, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********720481, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********720521, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********720547, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********720549, "dur": 657, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********721213, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********721243, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********721245, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********721287, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********721289, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********721319, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********721321, "dur": 323, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********721648, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********721678, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********721680, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********721744, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********721771, "dur": 824, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********722599, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********722633, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********722635, "dur": 168, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********722808, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********722835, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********722837, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********722876, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********722878, "dur": 822, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********723705, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********723740, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********723742, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********723782, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********723809, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********723811, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********723891, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********723920, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********723922, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********723959, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********724040, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********724070, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********724088, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********724128, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********724129, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********724190, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********724218, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********724220, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********724280, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********724312, "dur": 288, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********724604, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********724631, "dur": 5, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********724638, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********724680, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********724699, "dur": 173, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********724875, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********724907, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********724909, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********724936, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********724938, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********724994, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725019, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725045, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725072, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725103, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725129, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725131, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725161, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725163, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725193, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725259, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725288, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725290, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725317, "dur": 7, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725325, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725351, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725385, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725410, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725413, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725458, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725497, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725523, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725550, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725583, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725586, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725622, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725651, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725653, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725682, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725706, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725751, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725777, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725779, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725803, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725829, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725831, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725856, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725858, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725881, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725919, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725944, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********725981, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726006, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726008, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726035, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726067, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726094, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726120, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726157, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726187, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726247, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726285, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726286, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726312, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726363, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726369, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726402, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726434, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726458, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726488, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726514, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726543, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726575, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726599, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726601, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726627, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726662, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726693, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726695, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726728, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726756, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726758, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726782, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726783, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726824, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726844, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726864, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726889, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726891, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726931, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726933, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726960, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********726985, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********727010, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********727013, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********727044, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********727046, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********727080, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********727082, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********727108, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********727147, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********727150, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********727178, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********727180, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********727237, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********727290, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********727292, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********727338, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********727340, "dur": 256, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********727601, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********727636, "dur": 17, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********727655, "dur": 117, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********727776, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": **********727778, "dur": 6965054, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424692848, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424692854, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424692907, "dur": 24, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424692932, "dur": 4514, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424697453, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424697456, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424697492, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424697494, "dur": 12406, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424709911, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424709915, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424709966, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424709971, "dur": 102, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424710077, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424710079, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424710129, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424710133, "dur": 89299, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424799440, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424799443, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424799467, "dur": 20, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424799489, "dur": 19270, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424818770, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424818774, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424818799, "dur": 3, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424818803, "dur": 2310, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424821125, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424821130, "dur": 92, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424821226, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424821253, "dur": 22, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424821276, "dur": 7, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424821284, "dur": 103934, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424925228, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424925231, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424925280, "dur": 17, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424925299, "dur": 12584, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424937890, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424937893, "dur": 119, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424938015, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424938017, "dur": 9906, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424947936, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424947940, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424948007, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424948010, "dur": 394, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424948410, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424948437, "dur": 314, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752660424948754, "dur": 9862, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 6108, "tid": 6586, "ts": 1752660424979495, "dur": 2053, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 6108, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 6108, "tid": 8589934592, "ts": **********533953, "dur": 222843, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 6108, "tid": 8589934592, "ts": **********756799, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 6108, "tid": 8589934592, "ts": **********756807, "dur": 998, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 6108, "tid": 6586, "ts": 1752660424981551, "dur": 15, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 6108, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 6108, "tid": 4294967296, "ts": **********458658, "dur": 7502750, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 6108, "tid": 4294967296, "ts": **********465666, "dur": 55199, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 6108, "tid": 4294967296, "ts": 1752660424961693, "dur": 7076, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 6108, "tid": 4294967296, "ts": 1752660424966653, "dur": 46, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 6108, "tid": 4294967296, "ts": 1752660424968847, "dur": 17, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 6108, "tid": 6586, "ts": 1752660424981567, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": **********546424, "dur": 23709, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********570144, "dur": 1177, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********571442, "dur": 80, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": **********571522, "dur": 452, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********572191, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_6126AAA2007E3BC1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": **********573410, "dur": 337, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E0C67EE87749D19A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": **********581223, "dur": 240, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": **********572006, "dur": 17487, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********589505, "dur": 7360125, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752660424949633, "dur": 246, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752660424950037, "dur": 69, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752660424950141, "dur": 2134, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": **********572498, "dur": 17264, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********589769, "dur": 1173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_AC251BD94F5FD3AE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********590982, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_22C73FD68F7437D2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********591105, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_87AA2ADA08DFF7E1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********591160, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********591226, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_0BCE78C25C474DFA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********591281, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********591334, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_7B597B5EE040FE53.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********591582, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********591842, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_C7ED8541370EC3E2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********591935, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_EDF98A79C83CDC6C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********592572, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********593149, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********593214, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": **********593523, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********593583, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 1, "ts": **********593685, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********593769, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********593825, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 1, "ts": **********593877, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********594085, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********595148, "dur": 1530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********596679, "dur": 1588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********598267, "dur": 536, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Attributes\\ActiveInModeAttribute.cs"}}, {"pid": 12345, "tid": 1, "ts": **********598267, "dur": 2343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********600611, "dur": 1608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********602220, "dur": 1350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********603570, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********604451, "dur": 521, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 1, "ts": **********604021, "dur": 1001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********605023, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********605437, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********605880, "dur": 1360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********607240, "dur": 2459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********609699, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********610222, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********610683, "dur": 1798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********612481, "dur": 547, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_2_2.cs"}}, {"pid": 12345, "tid": 1, "ts": **********612481, "dur": 2189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********614670, "dur": 2141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********616811, "dur": 2279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********619090, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********619562, "dur": 2401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********621963, "dur": 2035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********623998, "dur": 2436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********626434, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********626857, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********627292, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********627846, "dur": 2759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********630756, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********630828, "dur": 1459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********632287, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********632743, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********632938, "dur": 911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********633849, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********634157, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********634535, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********634802, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********635456, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********635639, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********635916, "dur": 1063, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********636979, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********637160, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********637405, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********638025, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********638143, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********638279, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********638333, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********639016, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********639165, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********639302, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********639365, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********639762, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********639916, "dur": 1586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********641514, "dur": 71711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********713226, "dur": 2031, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********715259, "dur": 445, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********715714, "dur": 2021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********717736, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********717831, "dur": 3130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********720961, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********721045, "dur": 2021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********723067, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********723392, "dur": 1991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********725384, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********725462, "dur": 2824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********728287, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********728468, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********728736, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********728823, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********728907, "dur": 30641, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********763084, "dur": 187, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": **********763272, "dur": 962, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 1, "ts": **********764234, "dur": 52, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 1, "ts": **********759551, "dur": 4740, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********764291, "dur": 7185362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********572414, "dur": 17175, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********589600, "dur": 1079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_DD3CBC65A51B3B4A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********590680, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********590747, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_4D4853D2BE4AE8AE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********590856, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_B428977015ACB395.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********590947, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********591083, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_00FC4C225939AAE2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********591159, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_52799C164F48C7C2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********591219, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********591278, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_8824D3AD2222F26E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********591391, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_7C053C2C91326FF8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********591523, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********591578, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_AC0F081190478124.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********591905, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_744130CDF3C1ADFA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********592024, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********592099, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_BC3370F59F28D3D8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********592313, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 2, "ts": **********592394, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 2, "ts": **********592628, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": **********592749, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 2, "ts": **********592822, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********593512, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********593592, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 2, "ts": **********593726, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********593877, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********593971, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8863518860715653438.rsp"}}, {"pid": 12345, "tid": 2, "ts": **********594091, "dur": 1698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********595789, "dur": 1314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********597103, "dur": 2057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********599160, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********600305, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********601236, "dur": 1372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********602609, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********603381, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********603817, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********604270, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********605414, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********605820, "dur": 1598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********607418, "dur": 1969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********609388, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********609751, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********610247, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********611052, "dur": 1878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********612931, "dur": 1976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********614907, "dur": 2611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********617519, "dur": 2159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********619678, "dur": 1886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********621564, "dur": 2376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********623940, "dur": 2332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********626272, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********626755, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********627166, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********627662, "dur": 2714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********630377, "dur": 1469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********631847, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********632283, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********632752, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********633045, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********633512, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********633731, "dur": 678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********634439, "dur": 840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********635280, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********635481, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********635682, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********635976, "dur": 905, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********636882, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********636984, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********637173, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********637439, "dur": 588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********638027, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********638127, "dur": 3370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********641499, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********641668, "dur": 73667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********715342, "dur": 2007, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": **********717350, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********717456, "dur": 2233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": **********719690, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********719801, "dur": 3792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": **********723594, "dur": 989, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********724591, "dur": 2906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": **********727497, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********727647, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********727867, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********728012, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********728184, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********728350, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********728482, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********728726, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********728909, "dur": 35446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********764355, "dur": 7185258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********572487, "dur": 17198, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********589704, "dur": 1235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_31AB2D3F1E4A3D66.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********590970, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_F9331591B1619B3E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********591068, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_EF946CA7674E0325.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********591157, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_316ABDB813889A82.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********591268, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_1A6A88D66EF4C557.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********591347, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_C81CFFEB4B719421.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********591503, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********591569, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_BBA4FF1108562044.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********591780, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********591876, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_0BFC51EF888A92A4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********591976, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_70F846A33051C353.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********592098, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********592331, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********592521, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********592615, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********593003, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********593161, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": **********593275, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********593390, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********593652, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********593802, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********594056, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********595242, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********596233, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********596596, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********597877, "dur": 2264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********600141, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********600592, "dur": 1570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********602162, "dur": 1276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********603439, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********603873, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********604301, "dur": 632, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.State\\Transitions\\StateTransitionEditor.cs"}}, {"pid": 12345, "tid": 3, "ts": **********604301, "dur": 2492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********606794, "dur": 1682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********608477, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********609837, "dur": 741, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Runtime\\GPUDriven\\Debug\\DebugDisplayGPUResidentDrawer.cs"}}, {"pid": 12345, "tid": 3, "ts": **********608877, "dur": 2034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********612192, "dur": 898, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Variables\\VariablesPanel.cs"}}, {"pid": 12345, "tid": 3, "ts": **********610911, "dur": 3026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********613938, "dur": 2381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********616319, "dur": 1401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********617720, "dur": 1930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********619650, "dur": 2042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********622822, "dur": 544, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Reflection\\fsTypeCache.cs"}}, {"pid": 12345, "tid": 3, "ts": **********621693, "dur": 2653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********624347, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********624793, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********625534, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********625936, "dur": 1514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********627451, "dur": 1557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********629009, "dur": 1807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********630878, "dur": 1393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********632271, "dur": 666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********632937, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********633113, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********633289, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********633470, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********633852, "dur": 816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********634669, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********634805, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********635262, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********635434, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********635667, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********635888, "dur": 802, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********636690, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********636790, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********636889, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********636972, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********637215, "dur": 2778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********639995, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********640196, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********640643, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********640720, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********640788, "dur": 739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********641527, "dur": 71680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********713212, "dur": 2313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********715583, "dur": 2342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********717926, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********718046, "dur": 2703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********720750, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********720814, "dur": 2068, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********722883, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********723019, "dur": 2132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********725152, "dur": 463, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********725624, "dur": 3587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********729314, "dur": 7220271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********572620, "dur": 17195, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********589816, "dur": 911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_B52235DE51A256C2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********590754, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_B430B0CD9867C792.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********590859, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_3497E18D9ACE6C53.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********591015, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_85A7F339847C7C8F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********591110, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_A3DF7B62ADBC8E51.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********591178, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********591232, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_334958EB079AC540.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********591293, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********591393, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_A7499BA3B50749DD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********591496, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********591552, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_C5267BC5A847D8B9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********591700, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********591774, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_8E87AC3CC97FF6E8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********591853, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_C3A5EBA3221A6287.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********592062, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********592168, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********593506, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********593559, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 4, "ts": **********593652, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********593742, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********594022, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********594103, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********595155, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********596085, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********596681, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********597760, "dur": 2485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********600245, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********600873, "dur": 1958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********602831, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********603263, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********603657, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********603999, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********604442, "dur": 1454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********605897, "dur": 1745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********607642, "dur": 2523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********610165, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********610607, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********611056, "dur": 1884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********612941, "dur": 2611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********615552, "dur": 2158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********617711, "dur": 1992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********619703, "dur": 1975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********621679, "dur": 1879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********623558, "dur": 1646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********625205, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********625610, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********626017, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********626444, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********626849, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********627254, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********627717, "dur": 2044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********629761, "dur": 1905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********631666, "dur": 612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********632279, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********632752, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********632996, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********633597, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********633783, "dur": 479, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********634269, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********634493, "dur": 682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********635175, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********635326, "dur": 441, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********635800, "dur": 1200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********637000, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********637064, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********637162, "dur": 777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********637940, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********638060, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********638497, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********638601, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********638818, "dur": 671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********639489, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********639673, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********639800, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********640273, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********640403, "dur": 1121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********641524, "dur": 73797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********715333, "dur": 2207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********717547, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********717644, "dur": 2942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********720587, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********720653, "dur": 2643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********723298, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********723372, "dur": 2227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********725600, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********725661, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********725780, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********725845, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********725925, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********726022, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********726335, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********726415, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********726614, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********726758, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********726903, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********726977, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********727284, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********727436, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********727620, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********727765, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********728392, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********728556, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********728672, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********728864, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********728958, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********729022, "dur": 7220610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********572358, "dur": 17200, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********589581, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********589709, "dur": 959, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_F016717A46FE59BB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********590857, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_455623E8771FA1DB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********591018, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_E221DA127803E46F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********591246, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********591303, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_1FFECC68464BBF36.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********591385, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********591450, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_EFF9361EDEBC0A6E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********591503, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********591566, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_657053D7FE219A28.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********591701, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********591769, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_CE693D9B51C79752.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********591855, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_C763F25127858F24.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********592632, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********592847, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": **********593004, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********593224, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": **********593500, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********593558, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 5, "ts": **********593677, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********593770, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********593923, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********594068, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********594181, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********595331, "dur": 1436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********596767, "dur": 2115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********598883, "dur": 1667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********600550, "dur": 1492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********602043, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********603323, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********603780, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********604218, "dur": 1173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********605391, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********605882, "dur": 1365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********607248, "dur": 2007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********609255, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********609647, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********610122, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********610593, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********611010, "dur": 1731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********612741, "dur": 1996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********614737, "dur": 2719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********617456, "dur": 1916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********619373, "dur": 1115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********620489, "dur": 3068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********623558, "dur": 2295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********625853, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********626665, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********627066, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********627509, "dur": 2193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********629703, "dur": 1348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********631051, "dur": 1235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********632286, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********632748, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********632949, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": **********633378, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********633524, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********633755, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********633966, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********634062, "dur": 675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": **********634738, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********635200, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********635473, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********635641, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********635811, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": **********636353, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********636438, "dur": 700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": **********637164, "dur": 683, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********638732, "dur": 66633, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": **********713203, "dur": 2015, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": **********715219, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********715292, "dur": 2126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": **********717419, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********717497, "dur": 2165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": **********719668, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********719810, "dur": 2226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": **********722037, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********722273, "dur": 2190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": **********724464, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********724535, "dur": 2041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": **********726577, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********726658, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********726842, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********726911, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********726969, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********727122, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********727195, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********728028, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********728137, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********728282, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********728339, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********728400, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********728466, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********728563, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********728674, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********728738, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********728851, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********728919, "dur": 7210611, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752660424939588, "dur": 9891, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1752660424939533, "dur": 9948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": **********572398, "dur": 17174, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********589581, "dur": 1269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_E7120EB995DA210C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********590903, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_A52D67D727C640CA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********591108, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E0C67EE87749D19A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********591395, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********591485, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_BBF013107B6E7683.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********591583, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_0A8CC276CBDE6D9D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********591794, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********591870, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_3D6AFCA43E0B6878.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********591981, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_14BBB0A1A56B54F9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********592433, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********593304, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********593508, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********593567, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3064155424177982801.rsp"}}, {"pid": 12345, "tid": 6, "ts": **********593683, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********593736, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 6, "ts": **********593790, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********593889, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********593995, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********594084, "dur": 1378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********595463, "dur": 1302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********596766, "dur": 2456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********599222, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********599632, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********600274, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********600917, "dur": 1690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********602607, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********602998, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********603514, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********603969, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********604435, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.State\\States\\StateDescription.cs"}}, {"pid": 12345, "tid": 6, "ts": **********604435, "dur": 2528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********606963, "dur": 1260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********608223, "dur": 2230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********610454, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********610927, "dur": 2597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********613525, "dur": 2628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********616153, "dur": 1404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********617557, "dur": 2035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********619592, "dur": 2990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********622582, "dur": 2588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********625171, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********625587, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********625992, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********626946, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********627380, "dur": 1301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********628682, "dur": 2119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********630877, "dur": 1404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********632281, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********632755, "dur": 817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********633605, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": **********634212, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********634509, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********634713, "dur": 927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": **********635640, "dur": 979, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********636627, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********636853, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********636906, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********637235, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********637407, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********637498, "dur": 1341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": **********638840, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********638998, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********639156, "dur": 728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": **********639885, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********639984, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********640150, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": **********640593, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********640682, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********640783, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": **********641188, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********641281, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********641529, "dur": 71671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********713204, "dur": 4524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********717729, "dur": 781, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********718519, "dur": 2660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********721184, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********721348, "dur": 2041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********723390, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********723478, "dur": 2044, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********725523, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********725628, "dur": 2870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********728499, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********728919, "dur": 7091448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752660424820428, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752660424820373, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752660424820526, "dur": 2442, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752660424822974, "dur": 126655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********572440, "dur": 17160, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********589606, "dur": 1342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_36D94D0B0882E7EC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********590988, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_D8824977E7F2B23E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********591112, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_66BBE97858D0953F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********591167, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********591228, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_E8798D0BA79A4C08.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********591282, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********591386, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********591471, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_91BF7E65A37C8C47.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********591766, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********591858, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_84C61B5FFF6AE5F9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********591959, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_88B40F493262C216.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********592032, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_8A77BBF0F2259C97.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********592554, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********593493, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********593578, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 7, "ts": **********593891, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********594084, "dur": 1368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********595452, "dur": 1302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********596754, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********597879, "dur": 2044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********599924, "dur": 1367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********601292, "dur": 1513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********602806, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********603272, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********603691, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********604158, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********605109, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********605522, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********606452, "dur": 1664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********608116, "dur": 1913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********610029, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********610504, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********612258, "dur": 845, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\ResourceProviders\\IResourceProvider.cs"}}, {"pid": 12345, "tid": 7, "ts": **********610951, "dur": 3021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********613973, "dur": 2884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********616857, "dur": 2088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********618945, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********619460, "dur": 2570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********622031, "dur": 2306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********624337, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********624933, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********625584, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********626002, "dur": 1740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********627742, "dur": 1982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********629724, "dur": 1783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********631508, "dur": 759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********632272, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********632334, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********632745, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********632921, "dur": 659, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********633583, "dur": 1340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********634924, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********635097, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********635374, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********635561, "dur": 1488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********637050, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********637168, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********637231, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********637436, "dur": 1452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********638889, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********638985, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********639091, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********639516, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********639614, "dur": 1901, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********641516, "dur": 71698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********713217, "dur": 4403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": **********717621, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********717717, "dur": 2228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": **********719946, "dur": 2224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********722178, "dur": 2114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": **********724293, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********724351, "dur": 4232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": **********728585, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********728743, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********728870, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********729310, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********729375, "dur": 7220304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********572464, "dur": 17147, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********589618, "dur": 1327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_B8BEB69DE53F9C41.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********590985, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_2E8623D16E8E668C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********591415, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_1F0AB14E18D23E11.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********591525, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B3100D568D19DE53.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********591592, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********591763, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_2AE61BEFE92D422E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********592135, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********592287, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********592427, "dur": 38268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********630697, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********630828, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********630918, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********631067, "dur": 1123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********632300, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********632398, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********632743, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********632947, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********633513, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********633620, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********633814, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********634139, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********634647, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********634839, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********634990, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********635054, "dur": 781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********635835, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********636074, "dur": 463, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********636544, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********636702, "dur": 754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********637457, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********637590, "dur": 3941, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********641531, "dur": 71971, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********713509, "dur": 3574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": **********717084, "dur": 3265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********720359, "dur": 2485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": **********722845, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********722920, "dur": 2441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": **********725362, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********725433, "dur": 3783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": **********729217, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********729304, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********729384, "dur": 7220288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********572337, "dur": 17199, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********589571, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********589657, "dur": 1039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_01F0438719F4637C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********590729, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_81542F95A0721D6A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********590873, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********590948, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_DF6F7FF917E347B3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********591035, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_B3BE1B8334830D52.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********591169, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********591234, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_810264E72E8EDE2D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********591339, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_129E2D525C6714B0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********591455, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_31B99CCAA2C60608.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********591699, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********591974, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_F03F48A1E46251D9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********592127, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********592183, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********592398, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_7E6A236E3ABEDC63.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********592547, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********592747, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 9, "ts": **********592945, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********593114, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********593169, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": **********593458, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": **********593559, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********593614, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 9, "ts": **********593675, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********593908, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********594004, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********594092, "dur": 1188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********595280, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********596328, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********596739, "dur": 1968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********598707, "dur": 1334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********600042, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********601153, "dur": 1344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********602498, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********603378, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********603813, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********604472, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********605384, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********605799, "dur": 1842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********607641, "dur": 2192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********609833, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********610493, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********610944, "dur": 542, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Utilities\\MathfEx.cs"}}, {"pid": 12345, "tid": 9, "ts": **********612250, "dur": 829, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Utilities\\EditorApplicationUtility.cs"}}, {"pid": 12345, "tid": 9, "ts": **********610944, "dur": 2946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********613891, "dur": 2732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********616624, "dur": 2037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********618662, "dur": 1779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********620441, "dur": 2542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********622984, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********623509, "dur": 1424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********624933, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********625368, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********625785, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********626176, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********626693, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********627098, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********628365, "dur": 629, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@dfdbd02f5918\\UnityEditor.TestRunner\\TestRunner\\Utils\\TestListProvider.cs"}}, {"pid": 12345, "tid": 9, "ts": **********627627, "dur": 3088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********630822, "dur": 1458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********632280, "dur": 469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********632750, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********632962, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********633023, "dur": 536, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********633561, "dur": 1167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********634729, "dur": 562, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********635299, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********635689, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********635988, "dur": 674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********636662, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********637049, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********637192, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********637257, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********637843, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********637972, "dur": 3553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********641525, "dur": 71695, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********713222, "dur": 2037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": **********715260, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********715415, "dur": 1991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": **********717407, "dur": 442, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********717882, "dur": 4120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": **********722003, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********722270, "dur": 3139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": **********725410, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********725528, "dur": 2823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": **********728352, "dur": 586, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********728947, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********729009, "dur": 7220579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********572526, "dur": 17247, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********589780, "dur": 1100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_59C0854D1BDEB3BB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********590880, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********590951, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_AF66D8329BCEF71E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********591042, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_17831032FF1C6E20.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********591170, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_C89BF2893952DD2D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********591233, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********591286, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_F12DCDB48BAEBA17.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********591342, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********591428, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_8A562CA37CD388A9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********591576, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_C7086B172735D898.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********591773, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_72CE526178B0FB4C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********591828, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********592023, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_AE7BD4FEA5F1A611.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********592293, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********592593, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 10, "ts": **********593073, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": **********593369, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********593445, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": **********593521, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10397561839769426034.rsp"}}, {"pid": 12345, "tid": 10, "ts": **********593627, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 10, "ts": **********593805, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********593919, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********593994, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1488387367365330867.rsp"}}, {"pid": 12345, "tid": 10, "ts": **********594214, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********594328, "dur": 1321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********595650, "dur": 1829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********597480, "dur": 2370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********599851, "dur": 1537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********601389, "dur": 1803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********603192, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********603645, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********604098, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********605152, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********605662, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********606955, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********607357, "dur": 2502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********609859, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********610388, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********610823, "dur": 2201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********613024, "dur": 2641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********615665, "dur": 1500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********617166, "dur": 2142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********619309, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********619717, "dur": 2948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********622666, "dur": 1430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********624097, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********624894, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********625337, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********625752, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********626168, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********627257, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********627985, "dur": 2351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********630336, "dur": 1426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********631762, "dur": 507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********632274, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********632341, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********632776, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********633046, "dur": 659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": **********633706, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********633843, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": **********634429, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********634519, "dur": 1094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": **********635614, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********635690, "dur": 700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": **********636391, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********636495, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********636556, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********636633, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********636702, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********636981, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********637166, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********637471, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********637720, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": **********638221, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********638337, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********638395, "dur": 3105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********641501, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********641679, "dur": 71809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********713498, "dur": 2258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": **********715762, "dur": 1107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********716882, "dur": 2401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": **********719284, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********719429, "dur": 7361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": **********726791, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********726862, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********727124, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********727499, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********727564, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********727736, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********727895, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********728023, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********728095, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********728173, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": **********728227, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********728326, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********728589, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********728679, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********728795, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********728915, "dur": 6969784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660424698757, "dur": 10257, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752660424698704, "dur": 11752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752660424711564, "dur": 218, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752660424712375, "dur": 88771, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752660424820357, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752660424820342, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752660424820470, "dur": 2350, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752660424822828, "dur": 126814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********572548, "dur": 17237, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********589792, "dur": 890, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B76B86F6FFE10D96.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********590724, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_D09D54285D3D6DCD.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********590830, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********590896, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3B4D13D712F00EB8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********590994, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_201A952097D16B89.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********591070, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_29BCCE2A849A4ABC.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********591148, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_CE9F463A1284211A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********591217, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********591288, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_5AAA2FC2CAC01A77.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********591453, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_58C2D2C6FDB49C32.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********591571, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_982F16DC875BE786.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********591703, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********591768, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_2D8413BDE13CD7F1.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********591851, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_284B36A6DA56D348.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********591944, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_FB367C9346FF9A5D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********592367, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": **********592717, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********592970, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 11, "ts": **********593022, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********593223, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": **********593465, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 11, "ts": **********593528, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********593612, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 11, "ts": **********593771, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********593902, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********594072, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********594127, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********595356, "dur": 1433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********596790, "dur": 1789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********598580, "dur": 1456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********600036, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********600606, "dur": 1548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********602155, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********603549, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********603990, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********604436, "dur": 526, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.State\\Plugin\\Changelogs\\Changelog_1_4_0.cs"}}, {"pid": 12345, "tid": 11, "ts": **********604436, "dur": 2237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********606673, "dur": 1588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********608261, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********608790, "dur": 1403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********610193, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********610663, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********612537, "dur": 507, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_4_13.cs"}}, {"pid": 12345, "tid": 11, "ts": **********611069, "dur": 2555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********613624, "dur": 2812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********616436, "dur": 1850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********618287, "dur": 1508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********619795, "dur": 2494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********622289, "dur": 2349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********624638, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********625064, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********625463, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********625885, "dur": 1746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********627632, "dur": 1970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********629603, "dur": 976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********630579, "dur": 128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********630824, "dur": 1463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********632288, "dur": 624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********632913, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********633062, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********633129, "dur": 1227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": **********634356, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********634698, "dur": 658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": **********635357, "dur": 443, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********635839, "dur": 1035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": **********636874, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********636963, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********637211, "dur": 1959, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********639171, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********639353, "dur": 1267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": **********640624, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********640801, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********640903, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": **********641371, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********641501, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********641665, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": **********642014, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********642075, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": **********642771, "dur": 65, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********643740, "dur": 7050796, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752660424698994, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 11, "ts": 1752660424698690, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752660424699162, "dur": 9830, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1752660424699159, "dur": 10987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752660424711113, "dur": 232, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752660424712367, "dur": 214570, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752660424939452, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1752660424939437, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1752660424939563, "dur": 10027, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********572593, "dur": 17199, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********589800, "dur": 1066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_172A8F2500200F04.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********590866, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********590944, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_409E75408550DFD9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********591032, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_12994AB43863D35B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********591165, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_A42BD3F60EFF66D9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********591280, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_A9459742FC2B00F9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********591575, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********591695, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_2B2A94CF86E88A71.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********591860, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_403651568F253731.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********592187, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********592389, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********592595, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********592663, "dur": 29878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": **********622543, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********622634, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********622750, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********623381, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********623856, "dur": 2255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********626112, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********626616, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********627029, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********627507, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********628376, "dur": 2150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********630527, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********631581, "dur": 692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********632273, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********632779, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********633155, "dur": 1062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": **********634218, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********634297, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********634483, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********634711, "dur": 1031, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": **********635742, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********636043, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********636312, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": **********636811, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********636886, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********636941, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********637171, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********637427, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": **********637893, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********638032, "dur": 3491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********641523, "dur": 71699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********713223, "dur": 2004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": **********715229, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********715362, "dur": 1690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": **********717053, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********717307, "dur": 2062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": **********719370, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********719460, "dur": 2607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": **********722068, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********722144, "dur": 2054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": **********724198, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********724327, "dur": 3639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": **********727968, "dur": 426, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********728404, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********728562, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********728698, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********728754, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********728868, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********729281, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********729339, "dur": 7220353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752660424957374, "dur": 2428, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 6108, "tid": 6586, "ts": 1752660424983124, "dur": 7366, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 6108, "tid": 6586, "ts": 1752660424990555, "dur": 2755, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 6108, "tid": 6586, "ts": 1752660424976472, "dur": 18084, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}