{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 6108, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 6108, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 6108, "tid": 5000, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 6108, "tid": 5000, "ts": 1752657192162609, "dur": 1117, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 6108, "tid": 5000, "ts": 1752657192168105, "dur": 817, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 6108, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 6108, "tid": 1, "ts": 1752657190588890, "dur": 5350, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 6108, "tid": 1, "ts": 1752657190594245, "dur": 42218, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 6108, "tid": 1, "ts": 1752657190636476, "dur": 64338, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 6108, "tid": 5000, "ts": 1752657192168927, "dur": 12, "ph": "X", "name": "", "args": {}}, {"pid": 6108, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190587190, "dur": 20820, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190608014, "dur": 1544074, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190609287, "dur": 2910, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190612204, "dur": 1168, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190613375, "dur": 324, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190613706, "dur": 13, "ph": "X", "name": "ProcessMessages 20501", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190613721, "dur": 45, "ph": "X", "name": "ReadAsync 20501", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190613769, "dur": 1, "ph": "X", "name": "ProcessMessages 973", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190613771, "dur": 36, "ph": "X", "name": "ReadAsync 973", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190613808, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190613811, "dur": 32, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190613846, "dur": 1, "ph": "X", "name": "ProcessMessages 964", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190613848, "dur": 78, "ph": "X", "name": "ReadAsync 964", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190613930, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190613933, "dur": 374, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614311, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614313, "dur": 100, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614417, "dur": 5, "ph": "X", "name": "ProcessMessages 7003", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614424, "dur": 78, "ph": "X", "name": "ReadAsync 7003", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614506, "dur": 1, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614508, "dur": 80, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614592, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614595, "dur": 41, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614637, "dur": 2, "ph": "X", "name": "ProcessMessages 1475", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614640, "dur": 33, "ph": "X", "name": "ReadAsync 1475", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614676, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614679, "dur": 43, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614725, "dur": 2, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614728, "dur": 29, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614759, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614762, "dur": 28, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614792, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614794, "dur": 27, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614823, "dur": 2, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614826, "dur": 26, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614854, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614856, "dur": 25, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614883, "dur": 1, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614885, "dur": 28, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614915, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614917, "dur": 29, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614949, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614951, "dur": 27, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614980, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190614981, "dur": 25, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615008, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615011, "dur": 24, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615038, "dur": 29, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615070, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615072, "dur": 26, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615102, "dur": 27, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615131, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615133, "dur": 23, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615160, "dur": 43, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615206, "dur": 1, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615208, "dur": 38, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615248, "dur": 1, "ph": "X", "name": "ProcessMessages 896", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615250, "dur": 29, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615281, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615283, "dur": 22, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615309, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615339, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615340, "dur": 28, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615371, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615372, "dur": 25, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615400, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615402, "dur": 26, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615430, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615432, "dur": 25, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615459, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615461, "dur": 28, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615500, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615502, "dur": 37, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615542, "dur": 1, "ph": "X", "name": "ProcessMessages 885", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615545, "dur": 25, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615573, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615575, "dur": 26, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615603, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615605, "dur": 25, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615632, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615635, "dur": 26, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615664, "dur": 22, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615688, "dur": 1, "ph": "X", "name": "ProcessMessages 78", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615690, "dur": 34, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615729, "dur": 28, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615759, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615761, "dur": 26, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615789, "dur": 2, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615792, "dur": 23, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615819, "dur": 27, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615850, "dur": 37, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615889, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615891, "dur": 31, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615925, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615926, "dur": 33, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615963, "dur": 24, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190615992, "dur": 22, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616018, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616048, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616050, "dur": 26, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616078, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616080, "dur": 25, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616107, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616109, "dur": 22, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616135, "dur": 189, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616328, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616330, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616376, "dur": 1, "ph": "X", "name": "ProcessMessages 893", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616378, "dur": 33, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616413, "dur": 1, "ph": "X", "name": "ProcessMessages 737", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616417, "dur": 25, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616444, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616446, "dur": 31, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616479, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616481, "dur": 32, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616516, "dur": 1, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616520, "dur": 37, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616559, "dur": 1, "ph": "X", "name": "ProcessMessages 886", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616562, "dur": 23, "ph": "X", "name": "ReadAsync 886", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616587, "dur": 1, "ph": "X", "name": "ProcessMessages 91", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616590, "dur": 42, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616634, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616636, "dur": 33, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616672, "dur": 1, "ph": "X", "name": "ProcessMessages 846", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616675, "dur": 29, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616706, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616708, "dur": 26, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616736, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616738, "dur": 28, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616768, "dur": 1, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616770, "dur": 43, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616816, "dur": 1, "ph": "X", "name": "ProcessMessages 984", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616818, "dur": 26, "ph": "X", "name": "ReadAsync 984", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616846, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616849, "dur": 29, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616880, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616882, "dur": 26, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616910, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616913, "dur": 28, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616942, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616944, "dur": 25, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190616973, "dur": 29, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617005, "dur": 27, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617034, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617036, "dur": 26, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617065, "dur": 25, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617095, "dur": 28, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617125, "dur": 1, "ph": "X", "name": "ProcessMessages 885", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617127, "dur": 25, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617156, "dur": 36, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617194, "dur": 1, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617196, "dur": 22, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617222, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617253, "dur": 1, "ph": "X", "name": "ProcessMessages 1007", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617255, "dur": 26, "ph": "X", "name": "ReadAsync 1007", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617283, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617285, "dur": 27, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617313, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617315, "dur": 23, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617341, "dur": 26, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617371, "dur": 28, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617401, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617402, "dur": 27, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617431, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617433, "dur": 24, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617460, "dur": 24, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617487, "dur": 28, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617522, "dur": 1, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617523, "dur": 27, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617552, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617554, "dur": 28, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617585, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617586, "dur": 22, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617611, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617613, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617643, "dur": 29, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617675, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617677, "dur": 26, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617707, "dur": 27, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617735, "dur": 1, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617737, "dur": 25, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617764, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617766, "dur": 25, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617794, "dur": 29, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617825, "dur": 1, "ph": "X", "name": "ProcessMessages 943", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617827, "dur": 30, "ph": "X", "name": "ReadAsync 943", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617861, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617863, "dur": 25, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617892, "dur": 25, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617921, "dur": 31, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617954, "dur": 1, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617956, "dur": 27, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617985, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190617987, "dur": 25, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618016, "dur": 26, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618044, "dur": 1, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618046, "dur": 28, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618078, "dur": 1, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618080, "dur": 32, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618115, "dur": 1, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618117, "dur": 45, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618167, "dur": 31, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618201, "dur": 1, "ph": "X", "name": "ProcessMessages 1018", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618203, "dur": 27, "ph": "X", "name": "ReadAsync 1018", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618233, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618235, "dur": 24, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618263, "dur": 27, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618293, "dur": 32, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618328, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618329, "dur": 27, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618359, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618361, "dur": 25, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618388, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618390, "dur": 27, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618421, "dur": 27, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618450, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618452, "dur": 27, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618482, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618484, "dur": 24, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618512, "dur": 26, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618540, "dur": 1, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618542, "dur": 28, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618572, "dur": 1, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618574, "dur": 24, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618601, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618603, "dur": 22, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618629, "dur": 26, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618657, "dur": 1, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618660, "dur": 26, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618709, "dur": 1, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618711, "dur": 27, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618740, "dur": 253, "ph": "X", "name": "ProcessMessages 979", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190618995, "dur": 63, "ph": "X", "name": "ReadAsync 979", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619061, "dur": 4, "ph": "X", "name": "ProcessMessages 6499", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619067, "dur": 26, "ph": "X", "name": "ReadAsync 6499", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619095, "dur": 1, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619097, "dur": 26, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619125, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619126, "dur": 26, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619155, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619157, "dur": 23, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619182, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619184, "dur": 27, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619214, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619216, "dur": 26, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619244, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619246, "dur": 23, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619271, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619273, "dur": 25, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619300, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619302, "dur": 24, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619329, "dur": 27, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619359, "dur": 1, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619361, "dur": 25, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619388, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619389, "dur": 25, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619417, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619418, "dur": 24, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619445, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619447, "dur": 23, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619614, "dur": 85, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619702, "dur": 5, "ph": "X", "name": "ProcessMessages 5619", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619708, "dur": 42, "ph": "X", "name": "ReadAsync 5619", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619754, "dur": 2, "ph": "X", "name": "ProcessMessages 1037", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619757, "dur": 42, "ph": "X", "name": "ReadAsync 1037", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619800, "dur": 1, "ph": "X", "name": "ProcessMessages 1073", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619802, "dur": 24, "ph": "X", "name": "ReadAsync 1073", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619829, "dur": 31, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619863, "dur": 1, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619865, "dur": 29, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619897, "dur": 26, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619925, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619927, "dur": 23, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619953, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619956, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619995, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190619997, "dur": 29, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620028, "dur": 1, "ph": "X", "name": "ProcessMessages 787", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620030, "dur": 25, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620056, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620058, "dur": 34, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620095, "dur": 1, "ph": "X", "name": "ProcessMessages 919", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620097, "dur": 30, "ph": "X", "name": "ReadAsync 919", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620128, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620130, "dur": 24, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620157, "dur": 28, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620187, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620189, "dur": 30, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620222, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620225, "dur": 27, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620254, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620256, "dur": 24, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620284, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620288, "dur": 23, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620314, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620354, "dur": 1, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620356, "dur": 36, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620394, "dur": 1, "ph": "X", "name": "ProcessMessages 1038", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620397, "dur": 27, "ph": "X", "name": "ReadAsync 1038", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620426, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620428, "dur": 24, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620455, "dur": 30, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620488, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620490, "dur": 27, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620519, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620521, "dur": 29, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620552, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620554, "dur": 31, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620588, "dur": 1, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620590, "dur": 32, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620625, "dur": 1, "ph": "X", "name": "ProcessMessages 846", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620628, "dur": 29, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620659, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620661, "dur": 24, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620690, "dur": 24, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620718, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620761, "dur": 1, "ph": "X", "name": "ProcessMessages 869", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620763, "dur": 32, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620797, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620799, "dur": 27, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620828, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620830, "dur": 29, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620861, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620863, "dur": 32, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620897, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620899, "dur": 28, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620929, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620931, "dur": 26, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620960, "dur": 24, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190620987, "dur": 38, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621027, "dur": 1, "ph": "X", "name": "ProcessMessages 850", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621029, "dur": 29, "ph": "X", "name": "ReadAsync 850", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621061, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621063, "dur": 28, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621093, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621096, "dur": 24, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621123, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621165, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621211, "dur": 1, "ph": "X", "name": "ProcessMessages 757", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621213, "dur": 23, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621240, "dur": 78, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621321, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621355, "dur": 1, "ph": "X", "name": "ProcessMessages 773", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621357, "dur": 25, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621385, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621387, "dur": 74, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621466, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621502, "dur": 1, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621504, "dur": 24, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621532, "dur": 58, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621593, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621629, "dur": 1, "ph": "X", "name": "ProcessMessages 720", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621632, "dur": 34, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621667, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621670, "dur": 49, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621724, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621763, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621765, "dur": 24, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621791, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621793, "dur": 55, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621851, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621886, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621889, "dur": 27, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621917, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621920, "dur": 53, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190621977, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622011, "dur": 31, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622043, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622045, "dur": 57, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622104, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622106, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622148, "dur": 1, "ph": "X", "name": "ProcessMessages 950", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622150, "dur": 25, "ph": "X", "name": "ReadAsync 950", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622177, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622178, "dur": 54, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622237, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622268, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622270, "dur": 28, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622301, "dur": 68, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622373, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622406, "dur": 1, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622409, "dur": 27, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622438, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622440, "dur": 53, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622498, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622532, "dur": 2, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622535, "dur": 122, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622672, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622674, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622715, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622717, "dur": 30, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622749, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622751, "dur": 31, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622784, "dur": 4, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622790, "dur": 52, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622845, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622882, "dur": 1, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622886, "dur": 39, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622928, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622931, "dur": 60, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190622995, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623028, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623031, "dur": 28, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623060, "dur": 2, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623063, "dur": 69, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623135, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623174, "dur": 1, "ph": "X", "name": "ProcessMessages 843", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623177, "dur": 26, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623205, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623207, "dur": 50, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623261, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623300, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623304, "dur": 28, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623334, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623336, "dur": 56, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623396, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623434, "dur": 1, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623437, "dur": 27, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623467, "dur": 42, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623513, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623515, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623556, "dur": 1, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623558, "dur": 28, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623589, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623591, "dur": 95, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623691, "dur": 134, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623829, "dur": 3, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623833, "dur": 101, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623938, "dur": 2, "ph": "X", "name": "ProcessMessages 1251", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623943, "dur": 43, "ph": "X", "name": "ReadAsync 1251", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623990, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190623992, "dur": 47, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624043, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624045, "dur": 49, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624097, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624100, "dur": 289, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624392, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624395, "dur": 61, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624458, "dur": 2, "ph": "X", "name": "ProcessMessages 1422", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624461, "dur": 34, "ph": "X", "name": "ReadAsync 1422", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624497, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624498, "dur": 80, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624582, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624586, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624624, "dur": 2, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624627, "dur": 22, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624652, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624658, "dur": 27, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624687, "dur": 1, "ph": "X", "name": "ProcessMessages 713", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624688, "dur": 37, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624728, "dur": 1, "ph": "X", "name": "ProcessMessages 168", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624730, "dur": 34, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624766, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624768, "dur": 29, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624800, "dur": 88, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190624893, "dur": 122, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625020, "dur": 2, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625024, "dur": 33, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625062, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625066, "dur": 21, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625091, "dur": 29, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625124, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625126, "dur": 34, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625163, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625165, "dur": 30, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625197, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625199, "dur": 29, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625231, "dur": 32, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625265, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625267, "dur": 23, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625294, "dur": 126, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625424, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625427, "dur": 87, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625518, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625520, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625560, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625562, "dur": 30, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625595, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625597, "dur": 163, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625765, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625820, "dur": 1, "ph": "X", "name": "ProcessMessages 1060", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625822, "dur": 74, "ph": "X", "name": "ReadAsync 1060", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625899, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625902, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625936, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190625939, "dur": 60, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626002, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626039, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626042, "dur": 59, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626103, "dur": 1, "ph": "X", "name": "ProcessMessages 1160", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626105, "dur": 28, "ph": "X", "name": "ReadAsync 1160", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626136, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626138, "dur": 26, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626167, "dur": 27, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626198, "dur": 64, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626264, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626266, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626304, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626306, "dur": 30, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626338, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626340, "dur": 165, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626509, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626511, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626560, "dur": 1, "ph": "X", "name": "ProcessMessages 1078", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626562, "dur": 122, "ph": "X", "name": "ReadAsync 1078", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626687, "dur": 120, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626810, "dur": 2, "ph": "X", "name": "ProcessMessages 1095", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626813, "dur": 49, "ph": "X", "name": "ReadAsync 1095", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626865, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626868, "dur": 42, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626913, "dur": 43, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190626965, "dur": 33, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190627001, "dur": 128, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190627133, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190627166, "dur": 1, "ph": "X", "name": "ProcessMessages 1131", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190627168, "dur": 65, "ph": "X", "name": "ReadAsync 1131", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190627236, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190627263, "dur": 25, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190627291, "dur": 26, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190627319, "dur": 97, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190627418, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190627443, "dur": 24, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190627470, "dur": 21, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190627494, "dur": 97, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190627595, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190627597, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190627646, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190627648, "dur": 33, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190627684, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190627686, "dur": 129, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190627820, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190627866, "dur": 1, "ph": "X", "name": "ProcessMessages 1201", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190627868, "dur": 123, "ph": "X", "name": "ReadAsync 1201", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190627995, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628034, "dur": 1, "ph": "X", "name": "ProcessMessages 1237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628036, "dur": 116, "ph": "X", "name": "ReadAsync 1237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628156, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628197, "dur": 1, "ph": "X", "name": "ProcessMessages 1163", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628198, "dur": 31, "ph": "X", "name": "ReadAsync 1163", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628233, "dur": 88, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628327, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628367, "dur": 2, "ph": "X", "name": "ProcessMessages 1056", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628370, "dur": 26, "ph": "X", "name": "ReadAsync 1056", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628398, "dur": 65, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628466, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628500, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628502, "dur": 25, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628529, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628531, "dur": 59, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628594, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628628, "dur": 26, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628657, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628659, "dur": 55, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628718, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628750, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628752, "dur": 27, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628781, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628783, "dur": 63, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628851, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628893, "dur": 1, "ph": "X", "name": "ProcessMessages 897", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628896, "dur": 27, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628925, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628927, "dur": 42, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190628972, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629004, "dur": 1, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629006, "dur": 28, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629038, "dur": 59, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629101, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629141, "dur": 1, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629144, "dur": 29, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629175, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629177, "dur": 40, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629221, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629263, "dur": 2, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629266, "dur": 28, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629296, "dur": 11, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629310, "dur": 31, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629345, "dur": 31, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629378, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629380, "dur": 28, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629410, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629413, "dur": 44, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629461, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629500, "dur": 1, "ph": "X", "name": "ProcessMessages 990", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629502, "dur": 24, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629529, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629533, "dur": 67, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629603, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629642, "dur": 1, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629645, "dur": 27, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629673, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629675, "dur": 45, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629724, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629756, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629758, "dur": 45, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629806, "dur": 34, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629843, "dur": 46, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629893, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629895, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629937, "dur": 1, "ph": "X", "name": "ProcessMessages 871", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629939, "dur": 27, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629968, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190629969, "dur": 75, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630048, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630101, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630104, "dur": 34, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630140, "dur": 1, "ph": "X", "name": "ProcessMessages 858", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630142, "dur": 26, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630170, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630172, "dur": 23, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630198, "dur": 23, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630225, "dur": 71, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630300, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630342, "dur": 29, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630373, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630375, "dur": 42, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630419, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630421, "dur": 32, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630456, "dur": 5, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630462, "dur": 25, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630490, "dur": 24, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630519, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630573, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630612, "dur": 1, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630614, "dur": 30, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630646, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630648, "dur": 35, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630685, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630687, "dur": 25, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630717, "dur": 26, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630746, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630794, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630796, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190630827, "dur": 198, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190631032, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190631065, "dur": 382, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190631451, "dur": 433, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190631888, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190631890, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190631931, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190631934, "dur": 46, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190631984, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190631987, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632016, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632018, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632047, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632106, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632132, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632134, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632161, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632162, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632206, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632208, "dur": 25, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632235, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632237, "dur": 24, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632266, "dur": 21, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632291, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632328, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632333, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632363, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632365, "dur": 41, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632410, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632412, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632439, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632442, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632468, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632470, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632498, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632500, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632526, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632528, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632555, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632558, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632583, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632622, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632625, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632656, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632658, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632685, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632688, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632714, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632750, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632752, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632778, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632780, "dur": 121, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632905, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632907, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632950, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632952, "dur": 28, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632983, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190632986, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633013, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633014, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633039, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633041, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633067, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633095, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633120, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633123, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633149, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633173, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633176, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633213, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633215, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633241, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633244, "dur": 22, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633271, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633296, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633298, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633323, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633324, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633348, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633350, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633375, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633377, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633401, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633405, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633439, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633468, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633470, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633496, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633498, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633528, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633530, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633556, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633584, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633586, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633613, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633644, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633646, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633670, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633694, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633695, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633721, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633724, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633753, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633755, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633782, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633784, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633808, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633833, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633857, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633858, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633875, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633900, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633901, "dur": 24, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633927, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633929, "dur": 21, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633954, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633979, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190633981, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634009, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634033, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634036, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634063, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634065, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634090, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634092, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634114, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634117, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634143, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634145, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634171, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634173, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634196, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634198, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634222, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634224, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634250, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634276, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634300, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634302, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634328, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634354, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634356, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634382, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634420, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634422, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634450, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634452, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634476, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634478, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634514, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634546, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634549, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634577, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634579, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634611, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634613, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634646, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634685, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634687, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634718, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634720, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634745, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634748, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634774, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634798, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634800, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634825, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634851, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634892, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634895, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634935, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634963, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190634965, "dur": 35, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635004, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635031, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635034, "dur": 37, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635073, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635076, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635102, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635104, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635129, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635131, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635160, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635162, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635194, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635196, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635224, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635227, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635267, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635269, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635295, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635297, "dur": 21, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635321, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635324, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635354, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635357, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635384, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635385, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635409, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635411, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635438, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635439, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635465, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635466, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635495, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635497, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635534, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635564, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635566, "dur": 21, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635591, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635617, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635619, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635644, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635646, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635671, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635672, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635706, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635708, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635735, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635783, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635809, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635811, "dur": 20, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635835, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635863, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635889, "dur": 28, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635920, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635958, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635989, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190635991, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636016, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636017, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636056, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636087, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636112, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636137, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636173, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636211, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636214, "dur": 33, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636249, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636251, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636276, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636278, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636300, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636324, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636346, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636369, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636390, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636392, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636418, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636442, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636444, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636469, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636495, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636497, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636525, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636527, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636558, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636560, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636588, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636590, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636615, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636644, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636646, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636670, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636694, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636719, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636758, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636761, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636791, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636792, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636818, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636821, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636846, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636847, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636872, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636874, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636897, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636898, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636946, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190636975, "dur": 195, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190637174, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190637176, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190637209, "dur": 14048, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190651263, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190651266, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190651322, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190651326, "dur": 1694, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190653024, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190653027, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190653071, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190653074, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190653114, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190653187, "dur": 17, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190653206, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190653238, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190653240, "dur": 139, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190653384, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190653412, "dur": 12902, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190666320, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190666324, "dur": 400, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190666728, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190666731, "dur": 29, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190666763, "dur": 102, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190666869, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190666871, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190666913, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190666915, "dur": 171, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190667093, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190667124, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190667126, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190667151, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190667153, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190667190, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190667216, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190667251, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190667269, "dur": 179, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190667452, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190667482, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190667506, "dur": 197, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190667708, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190667734, "dur": 105, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190667842, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190667844, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190667869, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190667871, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190667896, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190667898, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190667965, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190667967, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668000, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668002, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668055, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668058, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668096, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668098, "dur": 147, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668250, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668278, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668280, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668307, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668333, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668335, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668362, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668387, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668417, "dur": 199, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668620, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668653, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668655, "dur": 77, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668736, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668761, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668762, "dur": 85, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668850, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668881, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668929, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190668955, "dur": 134, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669092, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669119, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669121, "dur": 121, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669246, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669293, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669325, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669373, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669400, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669403, "dur": 48, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669455, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669489, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669518, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669520, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669575, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669603, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669629, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669664, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669706, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669709, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669759, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669762, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669792, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669794, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669853, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669887, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669910, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669912, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669938, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190669999, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190670025, "dur": 451, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190670481, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190670521, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190670523, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190670550, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190670552, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190670578, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190670624, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190670652, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190670693, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190670695, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190670721, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190670747, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190670771, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190670794, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190670831, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190670858, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190670881, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190670883, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190670914, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190670938, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190670962, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190670985, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671008, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671031, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671054, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671056, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671085, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671107, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671108, "dur": 106, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671218, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671254, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671292, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671325, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671327, "dur": 46, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671377, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671406, "dur": 152, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671562, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671587, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671589, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671617, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671621, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671656, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671658, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671683, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671725, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671747, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671749, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671826, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671848, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190671850, "dur": 171, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190672026, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190672080, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190672083, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190672113, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190672148, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190672150, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190672185, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190672214, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190672216, "dur": 146, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190672368, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190672403, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190672406, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190672437, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190672439, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190672466, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190672561, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190672587, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190672637, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190672664, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190672666, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190672693, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190672718, "dur": 905, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190673631, "dur": 64, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190673698, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190673701, "dur": 72, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190673779, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190673822, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190673825, "dur": 124, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190673957, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190673990, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190673992, "dur": 415, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190674412, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190674415, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190674441, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190674443, "dur": 198, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190674645, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190674647, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190674694, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190674697, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190674735, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190674738, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190674769, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190674791, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190674816, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190674817, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190674887, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190674889, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190674917, "dur": 140, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190675061, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190675064, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190675109, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190675112, "dur": 470, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190675585, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190675588, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190675639, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190675641, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190675675, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190675676, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190675706, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190675708, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190675789, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190675819, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190675822, "dur": 112, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190675937, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190675938, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190675976, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190675978, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190676013, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190676063, "dur": 157, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190676226, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190676278, "dur": 130, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190676413, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190676415, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190676459, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190676461, "dur": 445, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190676910, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190676912, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190676960, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190677005, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190677008, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190677076, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190677113, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190677150, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190677152, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190677235, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190677237, "dur": 106, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190677359, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190677388, "dur": 256, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190677655, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190677687, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190677716, "dur": 204, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190677963, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190677976, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190678018, "dur": 396, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190678417, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190678419, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190678486, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190678491, "dur": 59336, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190737836, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190737840, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190737872, "dur": 1445, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190739323, "dur": 10147, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190749480, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190749484, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190749524, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190749526, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190749549, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190749588, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190749590, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190749619, "dur": 142, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190749765, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190749849, "dur": 166, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190750019, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190750048, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190750050, "dur": 598, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190750653, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190750698, "dur": 169, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190750871, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190750915, "dur": 1150, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190752070, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190752160, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190752162, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190752194, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190752196, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190752340, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190752369, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190752406, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190752432, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190752498, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190752541, "dur": 230, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190752774, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190752800, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190752802, "dur": 344, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190753150, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190753176, "dur": 758, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190753939, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190753969, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190753971, "dur": 221, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190754196, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190754220, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190754222, "dur": 288, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190754514, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190754538, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190754540, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190754661, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190754685, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190754711, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190754733, "dur": 104, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190754841, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190754873, "dur": 731, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190755608, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190755634, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190755664, "dur": 666, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190756334, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190756361, "dur": 169, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190756534, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190756637, "dur": 213, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190756853, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190756881, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190756883, "dur": 138, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190757026, "dur": 127, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190757156, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190757158, "dur": 94, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190757257, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190757339, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190757341, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190757369, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190757371, "dur": 482, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190757857, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190757859, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190757876, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190757909, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190757911, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190757989, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190758013, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190758015, "dur": 1150, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190759175, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190759179, "dur": 105, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190759289, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190759292, "dur": 386, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190759681, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190759684, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190759726, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190759728, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190759764, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190759766, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190759797, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190759799, "dur": 134, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190759937, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190759984, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190759986, "dur": 390, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190760379, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190760380, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190760411, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190760442, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190760444, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190760508, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190760533, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190760586, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190760609, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190760611, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190760635, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190760684, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190760710, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190760735, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190760771, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190760806, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190760850, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190760871, "dur": 45, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190760919, "dur": 66, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190760988, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190760991, "dur": 25, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761020, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761045, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761071, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761098, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761100, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761124, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761148, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761172, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761195, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761247, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761249, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761273, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761274, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761297, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761320, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761322, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761344, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761368, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761369, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761392, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761469, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761471, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761497, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761499, "dur": 23, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761524, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761534, "dur": 42, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761578, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761580, "dur": 24, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761607, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761609, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761658, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761661, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761693, "dur": 180, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761878, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761909, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761944, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761946, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190761977, "dur": 277, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190762259, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190762337, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190762378, "dur": 110, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190762493, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190762519, "dur": 32, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657190762552, "dur": 1030702, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191793267, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191793273, "dur": 140, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191793417, "dur": 23, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191793442, "dur": 11958, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191805410, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191805415, "dur": 116, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191805535, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191805540, "dur": 2362, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191807908, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191807911, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191807950, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191807954, "dur": 95799, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191903763, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191903767, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191903796, "dur": 38, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191903836, "dur": 19141, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191922990, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191923020, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191923090, "dur": 6, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191923102, "dur": 2090, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191925200, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191925203, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191925237, "dur": 82, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657191925321, "dur": 106807, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192032138, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192032141, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192032214, "dur": 21, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192032236, "dur": 20728, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192052974, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192052978, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192053037, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192053042, "dur": 65271, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192118323, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192118327, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192118381, "dur": 52, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192118435, "dur": 19704, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192138154, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192138159, "dur": 124, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192138288, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192138294, "dur": 620, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192138918, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192138921, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192139018, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192139024, "dur": 440, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192139468, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192139471, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192139513, "dur": 97, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192139614, "dur": 327, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192139946, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192139948, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192140032, "dur": 42, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192140077, "dur": 607, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192140689, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192140692, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192140744, "dur": 561, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 6108, "tid": 12884901888, "ts": 1752657192141309, "dur": 9810, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 6108, "tid": 5000, "ts": 1752657192168941, "dur": 3667, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 6108, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 6108, "tid": 8589934592, "ts": 1752657190584710, "dur": 116193, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 6108, "tid": 8589934592, "ts": 1752657190700906, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 6108, "tid": 8589934592, "ts": 1752657190700914, "dur": 4706, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 6108, "tid": 5000, "ts": 1752657192172610, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 6108, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 6108, "tid": 4294967296, "ts": 1752657190521920, "dur": 1631193, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 6108, "tid": 4294967296, "ts": 1752657190526519, "dur": 51912, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 6108, "tid": 4294967296, "ts": 1752657192153308, "dur": 5684, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 6108, "tid": 4294967296, "ts": 1752657192156602, "dur": 122, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 6108, "tid": 4294967296, "ts": 1752657192159080, "dur": 20, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 6108, "tid": 5000, "ts": 1752657192172616, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752657190605396, "dur": 3244, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752657190608679, "dur": 1395, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752657190610209, "dur": 80, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1752657190610289, "dur": 470, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752657190611716, "dur": 434, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_85A7F339847C7C8F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752657190612944, "dur": 1565, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7CC9B7F9B9108300.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752657190615149, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1752657190619825, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.Editor.ref.dll_E4491E98C71DB4B4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752657190620442, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752657190624491, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.CodeGen.dll"}}, {"pid": 12345, "tid": 0, "ts": 1752657190624854, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1752657190626591, "dur": 114, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752657190610796, "dur": 20810, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752657190631618, "dur": 1509252, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752657192140872, "dur": 186, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752657192141058, "dur": 121, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752657192141343, "dur": 68, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752657192141446, "dur": 2461, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752657190611135, "dur": 20532, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190631678, "dur": 881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_DD3CBC65A51B3B4A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752657190632560, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190632747, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190632810, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3B4D13D712F00EB8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752657190632886, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190632982, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190633115, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_D8824977E7F2B23E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752657190633262, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190633359, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190633494, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190633695, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190633877, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190634133, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190634332, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190634525, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190634788, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190635128, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190635519, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190635606, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190635661, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190635742, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190635816, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190635901, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190636003, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190636102, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190636231, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190636357, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190636502, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190636611, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190636702, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190636810, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190636937, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190637096, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190637314, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190637405, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190637524, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190637641, "dur": 1251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190638893, "dur": 915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190639809, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190640185, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190640807, "dur": 925, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\inspectors\\IInspectorChangeHandler.cs"}}, {"pid": 12345, "tid": 1, "ts": 1752657190640683, "dur": 1302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190641985, "dur": 2126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190644111, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190644592, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190644996, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190645409, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190647004, "dur": 598, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a55da47cc43f\\Runtime\\2D\\Shadows\\ShadowProvider\\Providers\\ShadowShapeProvider2D_Utility.cs"}}, {"pid": 12345, "tid": 1, "ts": 1752657190645857, "dur": 2434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190648291, "dur": 2245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190650537, "dur": 2564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190653102, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190653557, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190654027, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190654570, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190655028, "dur": 3095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190658124, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190658852, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190659342, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190660052, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190660447, "dur": 2185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190662632, "dur": 2015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190664647, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190665068, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190665528, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190666394, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190666621, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190667102, "dur": 582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190667691, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752657190667879, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190667970, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752657190668515, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190668860, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752657190669086, "dur": 942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752657190670028, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190670201, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752657190670658, "dur": 614, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190671282, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190671374, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190671617, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752657190672037, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190672095, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752657190672586, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190672887, "dur": 1685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190674574, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752657190674688, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190674754, "dur": 1551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752657190676306, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190676511, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190676571, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752657190676777, "dur": 850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752657190677627, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190677873, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190677949, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752657190678126, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752657190678460, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190678610, "dur": 69300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190747915, "dur": 2074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752657190749995, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190750226, "dur": 4305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752657190754536, "dur": 986, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190755527, "dur": 2495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752657190758023, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190758144, "dur": 2034, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752657190760180, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190760492, "dur": 2074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752657190762567, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752657190762771, "dur": 1378085, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190611072, "dur": 20557, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190631654, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190631773, "dur": 800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_01F0438719F4637C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752657190632573, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190632725, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190632836, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_409E75408550DFD9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752657190632893, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190633049, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190633191, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190633480, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190633698, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190634110, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190634186, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190634430, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190634541, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190634765, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190634936, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752657190635118, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190635249, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190635342, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752657190635424, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190635656, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190635829, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752657190635891, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190636076, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190636214, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190636365, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190636490, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190636613, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190636691, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190636813, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190636934, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190637095, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190637209, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190637529, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190637611, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752657190637666, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190637743, "dur": 1235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190638979, "dur": 1432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190640411, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190640889, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190641541, "dur": 2239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190643781, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190644210, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190644678, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190645082, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190646976, "dur": 609, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Graphs\\SerializableMesh.cs"}}, {"pid": 12345, "tid": 2, "ts": 1752657190645557, "dur": 2414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190647972, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190648405, "dur": 1628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190650034, "dur": 2439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190652473, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190652868, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190653312, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190653724, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190654166, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190654613, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190655031, "dur": 3219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190658250, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190658675, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190659129, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190659590, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190660087, "dur": 1854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190661942, "dur": 2274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190664217, "dur": 2319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190666631, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190667103, "dur": 838, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190667942, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752657190668175, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190668294, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752657190668446, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190668512, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752657190668664, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190668766, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752657190669034, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190669206, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752657190669375, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190669434, "dur": 921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752657190670356, "dur": 465, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190670829, "dur": 677, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190671562, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752657190672036, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190672211, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752657190672641, "dur": 530, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190673174, "dur": 3841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190677017, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752657190677140, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190677221, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752657190677503, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190677757, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190677812, "dur": 70055, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190747871, "dur": 2092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752657190749965, "dur": 416, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190750391, "dur": 2366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752657190752758, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190752866, "dur": 2575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752657190755449, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190755651, "dur": 2549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752657190758201, "dur": 501, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190758715, "dur": 2827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752657190761543, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190761813, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190762117, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190762401, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752657190762772, "dur": 1378043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190611127, "dur": 20523, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190631660, "dur": 1201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_E7120EB995DA210C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752657190632862, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190632956, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190633061, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190633183, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190633270, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190633449, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190633705, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190633875, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190634128, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190634270, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190634450, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190634542, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190634820, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190635011, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752657190635075, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190635355, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190635579, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190635676, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190635853, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1752657190635934, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190636088, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190636222, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190636340, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190636460, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190636610, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190636745, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190636910, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190637060, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190637352, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190637430, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752657190637482, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190637677, "dur": 1715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190639393, "dur": 1154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190640548, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190640979, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190641395, "dur": 2114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190643510, "dur": 1774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190645284, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190646920, "dur": 649, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Attributes\\BuiltinKeywordAttribute.cs"}}, {"pid": 12345, "tid": 3, "ts": 1752657190645690, "dur": 2469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190648160, "dur": 1478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190649638, "dur": 2153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190651791, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190652226, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190652658, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190653089, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190653521, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190654177, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190654624, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190655050, "dur": 3193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190658244, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190658677, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190659083, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190659516, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190660225, "dur": 2064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190662290, "dur": 2159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190664449, "dur": 396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190664845, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190665268, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190665677, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190666205, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190666683, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190667090, "dur": 816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190667907, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752657190668098, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190668309, "dur": 1207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752657190669517, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190669746, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190669897, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752657190670078, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190670184, "dur": 934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752657190671119, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190671363, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190671451, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190671546, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190671754, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190672011, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190672141, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752657190672489, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190672822, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752657190672877, "dur": 671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752657190673549, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190673714, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190673767, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752657190673943, "dur": 775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752657190674719, "dur": 692, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190675442, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190675509, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752657190675687, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752657190676237, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190676395, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190676454, "dur": 71408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190747864, "dur": 2066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752657190749931, "dur": 629, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190750600, "dur": 2399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752657190753000, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190753143, "dur": 1969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752657190755116, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190755328, "dur": 3129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752657190758461, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190758658, "dur": 2746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752657190761405, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190761618, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190761714, "dur": 649, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190762414, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752657190763165, "dur": 1377665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190611421, "dur": 20459, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190631893, "dur": 694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_172A8F2500200F04.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752657190632588, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190632709, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190632811, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_A52D67D727C640CA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752657190632876, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190632963, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190633109, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190633284, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_8824D3AD2222F26E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752657190633346, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190633517, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190633755, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190633915, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190634044, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190634146, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190634272, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190634330, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190634411, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190634496, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190634761, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190634817, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190635031, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190635183, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190635344, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190635634, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190636009, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190636113, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190636288, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190636409, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190636537, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190636668, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190636822, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190636939, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190637076, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190637273, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190637361, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190637438, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190637543, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190637656, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190637746, "dur": 1288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190639035, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190640197, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190640613, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190641093, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190641987, "dur": 2120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190644108, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190644686, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190645118, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190646918, "dur": 688, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Implementation\\IHasDependencies.cs"}}, {"pid": 12345, "tid": 4, "ts": 1752657190645542, "dur": 2242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190647784, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190648218, "dur": 1646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190649864, "dur": 1550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190651415, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190651855, "dur": 2308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190654164, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190654571, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190654981, "dur": 3070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190658052, "dur": 2818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190660871, "dur": 2249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190665155, "dur": 529, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\Noise\\cellular2D.cs"}}, {"pid": 12345, "tid": 4, "ts": 1752657190663121, "dur": 2563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190665685, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190666108, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190666766, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190667098, "dur": 591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190667690, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752657190667870, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190667929, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752657190668471, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190668767, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190668823, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752657190669062, "dur": 674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752657190669737, "dur": 794, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190670591, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752657190670750, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190670806, "dur": 705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752657190671512, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190671671, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190671907, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190672214, "dur": 414, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190672637, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752657190672854, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752657190672923, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752657190673355, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190673486, "dur": 38266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190711753, "dur": 36121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190747877, "dur": 3165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752657190751043, "dur": 624, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190751678, "dur": 2153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752657190753832, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190753959, "dur": 2267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752657190756227, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190756414, "dur": 2240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752657190758655, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190758811, "dur": 2505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752657190761321, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190761750, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 4, "ts": 1752657190761953, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190762219, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190762334, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657190762433, "dur": 1161184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752657191923654, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1752657191923620, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1752657191923810, "dur": 2243, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1752657191926057, "dur": 214754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190611383, "dur": 20468, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190631856, "dur": 770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_59C0854D1BDEB3BB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752657190632627, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190632712, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190632770, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_455623E8771FA1DB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752657190632847, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190632941, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190633042, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190633172, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190633304, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190633449, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190633546, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_657053D7FE219A28.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752657190633715, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190634020, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190634189, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190634280, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190634454, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190634615, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190634808, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190634888, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190635033, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190635264, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190635386, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190635515, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190635679, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190635755, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190635941, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190636040, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190636165, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190636301, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190636422, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190636540, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190636666, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190636792, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190637000, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190637141, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190637218, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190637484, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190637617, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190637686, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190638983, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190640276, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190640771, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190641188, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190642466, "dur": 2281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190644748, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190645164, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190646932, "dur": 638, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Graphs\\Matrix2MaterialSlot.cs"}}, {"pid": 12345, "tid": 5, "ts": 1752657190645675, "dur": 2168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190647843, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190648255, "dur": 1535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190649791, "dur": 1710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190651501, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190651925, "dur": 2080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190654036, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190654449, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190654872, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190655284, "dur": 3203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190658487, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190658910, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190659371, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190659830, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190660237, "dur": 2421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190662659, "dur": 1989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190664648, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190665089, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190665492, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190665910, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190666326, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190666720, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190667089, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190667689, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752657190667862, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190668003, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752657190668478, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190668699, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752657190668871, "dur": 1086, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752657190669957, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190670112, "dur": 846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752657190670959, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190671275, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752657190671475, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752657190672024, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190672186, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752657190672402, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190672480, "dur": 679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752657190673161, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190673370, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190673459, "dur": 32974, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190710508, "dur": 187, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 5, "ts": 1752657190710695, "dur": 990, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 5, "ts": 1752657190711685, "dur": 56, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 5, "ts": 1752657190706434, "dur": 5313, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190711747, "dur": 36704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190748454, "dur": 2809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752657190751264, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190751485, "dur": 2676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752657190754162, "dur": 833, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190755008, "dur": 1999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752657190757014, "dur": 640, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190757663, "dur": 2582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752657190760257, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190760579, "dur": 1967, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752657190762547, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752657190762700, "dur": 1378119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190611396, "dur": 20478, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190631880, "dur": 941, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B76B86F6FFE10D96.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752657190632822, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190632982, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190633171, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190633260, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190633374, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190633477, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190633550, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_BBA4FF1108562044.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752657190633728, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190633849, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190633978, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190634075, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190634200, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190634315, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752657190634413, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190634530, "dur": 17326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752657190651858, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190652110, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190652539, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190652942, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190653366, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190653771, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190654454, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190654888, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190655358, "dur": 3303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190658662, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190659077, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190659559, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190659967, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190660593, "dur": 2307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190662901, "dur": 2250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190665741, "dur": 523, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Tool\\IsExeAvailable.cs"}}, {"pid": 12345, "tid": 6, "ts": 1752657190665151, "dur": 1247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190666508, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190666630, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190667096, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190667693, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752657190667857, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190667935, "dur": 1056, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752657190668992, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190669201, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752657190669352, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190669542, "dur": 705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752657190670247, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190670440, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752657190670658, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190670713, "dur": 1434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752657190672147, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190672419, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190672605, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752657190672816, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190672937, "dur": 1273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752657190674211, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190674431, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190674578, "dur": 73287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190747866, "dur": 2801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752657190750668, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190750827, "dur": 2286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752657190753114, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190753309, "dur": 3043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752657190756353, "dur": 1509, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190757874, "dur": 2470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752657190760348, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190760754, "dur": 2209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752657190762964, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190763065, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752657190763130, "dur": 1377720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190611112, "dur": 20529, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190631654, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190631785, "dur": 830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_F016717A46FE59BB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752657190632615, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190632724, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190633052, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190633190, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190633325, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190633405, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_1F0AB14E18D23E11.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752657190633479, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190633566, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190633735, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_72CE526178B0FB4C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752657190633786, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190633902, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190634179, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190634290, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190634397, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190634478, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190634617, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190634879, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190634941, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190635004, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190635105, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190635168, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190635261, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190635336, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190635393, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190635445, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1752657190635559, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190635756, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190635857, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190635915, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190635982, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190636080, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190636158, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190636255, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190636387, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190636509, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190636623, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190636734, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190636875, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190637006, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190637236, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190637339, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190637456, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190637552, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190637684, "dur": 1744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190639429, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190640685, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190641324, "dur": 2150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190643475, "dur": 2015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190646968, "dur": 626, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Interfaces\\IMayRequireInstanceID.cs"}}, {"pid": 12345, "tid": 7, "ts": 1752657190645491, "dur": 2590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190648081, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190649222, "dur": 1586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190650809, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190651230, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190651646, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190654243, "dur": 878, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Runtime\\GPUDriven\\BatchLayers.cs"}}, {"pid": 12345, "tid": 7, "ts": 1752657190652072, "dur": 3396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190658633, "dur": 606, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Runtime\\Debugging\\MousePositionDebug.cs"}}, {"pid": 12345, "tid": 7, "ts": 1752657190655468, "dur": 3771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190659240, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190659736, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190660170, "dur": 2134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190662305, "dur": 2160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190664465, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190664870, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190665289, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190665704, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190666046, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190666635, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190667187, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190667731, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752657190667898, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190667972, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752657190668602, "dur": 1127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190669744, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190669924, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752657190670297, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752657190670642, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190670741, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752657190671230, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190671455, "dur": 1149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1752657190672634, "dur": 244, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190673558, "dur": 65073, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1752657190747862, "dur": 2115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752657190749978, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190750230, "dur": 2191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752657190752422, "dur": 730, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190753157, "dur": 2087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752657190755245, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190755469, "dur": 2000, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752657190757470, "dur": 319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190757804, "dur": 2035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752657190759848, "dur": 1536, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190761571, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190762415, "dur": 817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752657190763281, "dur": 1377547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190611254, "dur": 20529, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190631790, "dur": 1047, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_31AB2D3F1E4A3D66.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752657190632838, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190633065, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190633229, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190633314, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190633449, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190633574, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_982F16DC875BE786.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752657190633683, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190633786, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190633874, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190633961, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_6126AAA2007E3BC1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752657190634044, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190634190, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190634334, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190634420, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190634573, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190634710, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190634803, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190635016, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190635098, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190635308, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190635384, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190635532, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190635603, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190635666, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190635753, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190635813, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190635913, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190635999, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190636130, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190636224, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190636376, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190636533, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190636624, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190636723, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190636857, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190636982, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190637133, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190637285, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190637445, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752657190637499, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190637628, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190637696, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190638912, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190639866, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190640257, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190640678, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190641123, "dur": 2423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190643547, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190644271, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190644674, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190645075, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190646940, "dur": 649, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Graphs\\Vector1ShaderProperty.cs"}}, {"pid": 12345, "tid": 8, "ts": 1752657190645546, "dur": 2601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190648147, "dur": 1913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190650061, "dur": 1943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190654170, "dur": 936, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Runtime\\GPUDriven\\InstanceData\\InstanceWindDataUpdateDefs.cs"}}, {"pid": 12345, "tid": 8, "ts": 1752657190652004, "dur": 4254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190658632, "dur": 548, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Runtime\\Debugging\\DebugFrameTiming.cs"}}, {"pid": 12345, "tid": 8, "ts": 1752657190656259, "dur": 3272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190659532, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190659942, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190660344, "dur": 1676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190662020, "dur": 2158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190665100, "dur": 505, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\Il2CppEagerStaticClassConstructionAttribute.cs"}}, {"pid": 12345, "tid": 8, "ts": 1752657190664178, "dur": 1675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190665904, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190666130, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190666704, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190667131, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190667683, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752657190667822, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190667893, "dur": 1325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752657190669218, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190669567, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190669659, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752657190669838, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190669918, "dur": 2101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752657190672020, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190672433, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752657190672566, "dur": 426, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190673000, "dur": 1516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752657190674518, "dur": 678, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190675426, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190675624, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752657190675768, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190675845, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752657190676260, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190676594, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190676737, "dur": 71179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190747919, "dur": 2066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752657190749987, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190750352, "dur": 2447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752657190752800, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190752988, "dur": 2299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752657190755288, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190755471, "dur": 4149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752657190759639, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190759963, "dur": 2262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752657190762225, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190762400, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657190762453, "dur": 1376405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752657192138919, "dur": 749, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1752657192138860, "dur": 810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1752657192139688, "dur": 1062, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752657190611234, "dur": 20530, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190631780, "dur": 850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_B8BEB69DE53F9C41.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752657190632631, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190632761, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_B428977015ACB395.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752657190632836, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190632971, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190633163, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190633261, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190633407, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190633490, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190633738, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_8E87AC3CC97FF6E8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752657190633796, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190633930, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190634209, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190634274, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190634392, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190634501, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190634651, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190634762, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190634835, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190634950, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752657190635026, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190635161, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190635283, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190635375, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190635462, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190635542, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752657190635641, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190635776, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190635859, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190635921, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190636058, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190636197, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190636329, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190636436, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190636619, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190636723, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190636810, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190636899, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190637084, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190637365, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190637549, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190637673, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190638545, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190639624, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190640341, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190640768, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190641246, "dur": 1881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190643127, "dur": 1959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190645086, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190645488, "dur": 1351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190646998, "dur": 682, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a55da47cc43f\\Runtime\\2D\\Passes\\IRenderPass2D.cs"}}, {"pid": 12345, "tid": 9, "ts": 1752657190646839, "dur": 2243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190649082, "dur": 1889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190650971, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190651374, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190651792, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190652212, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190652666, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190653178, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190653639, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190654379, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190654816, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190655261, "dur": 3151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190658412, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190658848, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190659329, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190659883, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190660297, "dur": 2166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190662464, "dur": 2128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190664592, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190665057, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190665738, "dur": 901, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Configuration\\AutoConfig.cs"}}, {"pid": 12345, "tid": 9, "ts": 1752657190665461, "dur": 1299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190666760, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190667127, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190667681, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752657190667908, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190667994, "dur": 692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752657190668687, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190668884, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752657190669075, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190669128, "dur": 1029, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752657190670157, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190670303, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752657190670510, "dur": 705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752657190671216, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190671370, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190671489, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190671551, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190671905, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190672142, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752657190672355, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190672534, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752657190673002, "dur": 509, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190673519, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190673573, "dur": 74298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190747873, "dur": 2828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752657190750702, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190750824, "dur": 2356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752657190753181, "dur": 1534, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190754730, "dur": 2460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752657190757195, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190757345, "dur": 3485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752657190760831, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190761239, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190761318, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190761493, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657190761756, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752657190762431, "dur": 1036195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657191798689, "dur": 6967, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752657191798631, "dur": 8608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752657191808455, "dur": 218, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752657191809177, "dur": 95360, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752657191923632, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752657191923612, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752657191923812, "dur": 2179, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752657191925999, "dur": 214868, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190611204, "dur": 20476, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190631690, "dur": 1326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_36D94D0B0882E7EC.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752657190633113, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190633247, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_29BCCE2A849A4ABC.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752657190633335, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190633537, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190633762, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190633996, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_AE7BD4FEA5F1A611.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752657190634072, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190634203, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752657190634308, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190634449, "dur": 18995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752657190653445, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190653786, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190653928, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752657190654107, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190654183, "dur": 12774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752657190666958, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190667139, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752657190667249, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752657190667534, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190667682, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752657190667896, "dur": 701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752657190668597, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190668864, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190669179, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752657190669362, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190669420, "dur": 650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752657190670071, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190670266, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752657190670418, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190670562, "dur": 1315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752657190671877, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190672045, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190672140, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752657190672390, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752657190672879, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190673178, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190673245, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752657190673442, "dur": 741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752657190674185, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190674389, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190674456, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752657190674637, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190674781, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752657190675122, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190675450, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190675571, "dur": 72289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190747883, "dur": 2040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752657190749924, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190750294, "dur": 2765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752657190753060, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190753215, "dur": 2132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752657190755348, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190755522, "dur": 2237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752657190757760, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190758017, "dur": 4028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752657190762046, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190762399, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752657190762732, "dur": 1378081, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190611373, "dur": 20469, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190631849, "dur": 751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_AC251BD94F5FD3AE.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752657190632601, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190632681, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_81542F95A0721D6A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752657190632737, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190632794, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_3497E18D9ACE6C53.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752657190632907, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190633062, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190633263, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190633370, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190633491, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190633713, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190633781, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_284B36A6DA56D348.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752657190633838, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190634041, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190634209, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190634312, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190634424, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190634544, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190634617, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_BC4BCFD1CE67FD5E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752657190634716, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190634774, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190634831, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190635001, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190635157, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190635334, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190635458, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190635673, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190635942, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190636019, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190636148, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190636272, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190636414, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190636553, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190636683, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190636798, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190636911, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190637045, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190637475, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190637650, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190637966, "dur": 1089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190639055, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190640247, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190640771, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190641174, "dur": 690, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center@f3fb577b3546\\Editor\\MultiplayerCenterWindow\\UI\\RecommendationView\\RecommendationItemView.cs"}}, {"pid": 12345, "tid": 11, "ts": 1752657190641174, "dur": 2773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190643948, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190644429, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190644894, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190645308, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190646954, "dur": 564, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a55da47cc43f\\Runtime\\2D\\Renderer2DDataAuthoring.cs"}}, {"pid": 12345, "tid": 11, "ts": 1752657190645911, "dur": 2323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190648234, "dur": 1649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190649883, "dur": 1943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190651826, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190652233, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190652622, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190653128, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190653551, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190654093, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190654566, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190654973, "dur": 1580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190658609, "dur": 535, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Runtime\\Common\\IVirtualTexturingEnabledRenderPipeline.cs"}}, {"pid": 12345, "tid": 11, "ts": 1752657190656554, "dur": 3059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190659613, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190660180, "dur": 2683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190662863, "dur": 1931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190664795, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190665223, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190665621, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190666043, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190666635, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190667095, "dur": 591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190667687, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752657190667896, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190668060, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752657190668644, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190669058, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190669161, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752657190669845, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190670052, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752657190670563, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190670709, "dur": 984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752657190671693, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190671815, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190671909, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190672209, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752657190672407, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752657190672922, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190673174, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190673237, "dur": 5269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190678508, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752657190678653, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190678706, "dur": 71252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190749960, "dur": 3500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752657190753461, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190753585, "dur": 2112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752657190755698, "dur": 1434, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190757140, "dur": 3270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752657190760424, "dur": 337, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190760768, "dur": 2383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752657190763156, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752657190763247, "dur": 1377587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190611429, "dur": 20471, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190631902, "dur": 937, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_B52235DE51A256C2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752657190632840, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190633174, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190633322, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190633409, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_8A562CA37CD388A9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752657190633482, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190634113, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190634268, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190634385, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752657190634630, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752657190634683, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190634785, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190634908, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1752657190634964, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752657190635029, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190635447, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1752657190635584, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752657190635878, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190636014, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190636160, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190636339, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190636498, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190636640, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190636754, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190636892, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190637006, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190637137, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190637309, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190637396, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190637523, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190637675, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190638942, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190640117, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190640910, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190641345, "dur": 2367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190643712, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190644398, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190644839, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190645277, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190647005, "dur": 612, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Graphs\\CubemapShaderProperty.cs"}}, {"pid": 12345, "tid": 12, "ts": 1752657190645679, "dur": 2257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190647936, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190648358, "dur": 2100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190650458, "dur": 1711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190652170, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190652588, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190652994, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190653566, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190654240, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190654659, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190655193, "dur": 4064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190659257, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190659755, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190660162, "dur": 2058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190662220, "dur": 2198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190664419, "dur": 1064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190665484, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190665936, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190666369, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190666776, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190667187, "dur": 717, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190667906, "dur": 1073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752657190668980, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190669064, "dur": 979, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752657190670043, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190670324, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190670388, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752657190670575, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190670694, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752657190671410, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190671527, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752657190671606, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190671669, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752657190671823, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190671878, "dur": 832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752657190672710, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190672997, "dur": 2687, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190675686, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752657190675822, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190675906, "dur": 782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752657190676689, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190676863, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190677009, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752657190677126, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190677198, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752657190677545, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190677751, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752657190677914, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752657190678286, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190678437, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657190678499, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752657190678643, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752657190680024, "dur": 1113999, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752657191799010, "dur": 6734, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 12, "ts": 1752657191798541, "dur": 7261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752657191806069, "dur": 71, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657191807038, "dur": 225875, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752657192036126, "dur": 15279, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1752657192036111, "dur": 16396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752657192053411, "dur": 196, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752657192054239, "dur": 64813, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752657192138776, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1752657192138761, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1752657192138884, "dur": 1377, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1752657192140269, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752657192148953, "dur": 2260, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 6108, "tid": 5000, "ts": 1752657192173491, "dur": 3377, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 6108, "tid": 5000, "ts": 1752657192176951, "dur": 2513, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 6108, "tid": 5000, "ts": 1752657192166455, "dur": 13868, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}